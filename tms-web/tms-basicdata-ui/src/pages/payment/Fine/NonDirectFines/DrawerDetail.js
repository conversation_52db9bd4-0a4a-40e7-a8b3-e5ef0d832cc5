import { connect } from 'umi';
import React, { useRef, useMemo, useCallback, useEffect, useState } from 'react';
import moment from 'moment';
import { Drawer, Button, message, Col, Descriptions, Divider, Row, Spin, Timeline, Table } from 'antd';
import Decimal from 'decimal.js';
import { useDispatch, useSelector } from 'dva';
import { debounce } from 'lodash';
import DrawerFooter from '@/components/DrawerFooter';
import FormContent from '@/components/FormContent';
import { baseItem } from './item';
import { asyncAwaitForm, renderOptions, getUids } from '@/utils/utils';

/**
 * 非直营罚款上报详情组件
 * @component
 */
const DetailTables = (props) => {
  const {
    queryInfo,
    isView,
    drawerVisible,
    dispatch,
    loading,
    handleSelectSearch,
    orderList = [],
    refreshPage,
    commonInterface: { CategoryData = [], allCarrierList = [] },
    carrierVehicle: { selectOutCarList = [] },
  } = props;
  const [isDeduct, setIsDeduct] = useState(false);
  const [deductTypeAttr, setDeductTypeAttr] = useState(null);
  // 扣款类型值
  const [deductTypeValue, setDeductTypeValue] = useState(null);
  // 是否为单边缺车
  const [isOnesidedLackOfVehicles, setIsOnesidedLackOfVehicles] = useState(false);
  // 是否内空不足
  const [isInnerLack, setIsInnerLack] = useState(false);
  const baseInfoEndRef = useRef();
  // 罚款金额文字展示
  const [penaltyMoneyText, setPenaltyMoneyText] = useState('罚款金额');
  // 罚款金额1文字展示
  const [penaltyMoney1Text, setPenaltyMoney1Text] = useState('罚款金额1');
  // 定标标准
  const [pricingStandard, setPricingStandard] = useState('定标标准');
  // 实际执行
  const [actualExecution, setActualExecution] = useState('实际执行');
  // 实际执行
  useEffect(() => {
    if (queryInfo?.penaltyType?.name === 'DEDUCT') {
      setIsDeduct(true);
    } else {
      setIsDeduct(false);
    }
    if (queryInfo?.servicerName) {
      handleSelectSearch('servicerName', queryInfo?.servicerName);
    } else {
      handleSelectSearch('servicerName');
    }
    if (queryInfo?.defineSupplier) {
      handleSelectSearch('defineSupplier', queryInfo?.defineSupplier);
    } else {
      handleSelectSearch('defineSupplier');
    }
    // 根据详情参数 异常子类 动态 label 回调
    if (queryInfo?.errorDescription) {
      console.log('%c [ queryInfo?.errorDescription ]: ', 'color: #bf2c9f; background: pink; font-size: 13px;', queryInfo?.errorDescription);
      handleErrorDescriptionChange(queryInfo?.errorDescription);
    }
    // 根据详情参数 扣款类型 动态 label 回调
    if (queryInfo?.deductType) {
      console.log('%c [ queryInfo?.deductType ]: ', 'color: #bf2c9f; background: pink; font-size: 13px;', queryInfo?.deductType.name);
      handleDeductTypeChange(queryInfo?.deductType?.name);
    }
  }, [queryInfo]);
  // 更新状态
  const updateData = useCallback(
    (payload) => {
      dispatch({
        type: 'NonDirectFines/updateData',
        payload,
      });
    },
    [dispatch],
  );

  // 关闭弹窗
  const onClose = useCallback(() => {
    updateData({
      drawerVisible: false,
      tableName: '',
      isView: false,
      queryInfo: {},
    });
  }, [updateData]);

  /**
   * 设置表单字段值
   * @param {Object} values - 要设置的字段值
   */
  const setFieldsValue = useCallback((values) => {
    baseInfoEndRef.current?.setFieldsValue(values);
  }, []);

  const getFieldValue = useCallback((name) => {
    return baseInfoEndRef.current?.getFieldValue(name);
  }, []);

  /**
   * 处理罚款金额计算
   * @param {string} type - 类型
   * @param {number} value - 值
   * @returns {Object} - 返回对象
   */
  const calculateFinalMoney = useCallback((type, value) => {
    // 过滤除数字和小数以外的内容，并将值转换为数字
    let processedValue = 0;
    if (value) {
      // 如果是字符串，去除非数字和小数点字符
      if (typeof value === 'string') {
        const numericValue = value.replace(/[^0-9.-]/g, '');
        // 尝试将处理后的字符串转换为数值
        const number = Number(numericValue);
        processedValue = Number.isNaN(number) ? 0 : number;
      } else {
        processedValue = value;
      }
    }

    if (type === 'penaltyMoney1') {
      const penaltyMoney1 = new Decimal(processedValue || 0);
      const penaltyMoney = new Decimal(baseInfoEndRef.current.getFieldValue('penaltyMoney') || 0);
      const finalMoney = penaltyMoney.plus(penaltyMoney1).toNumber();
      return {
        penaltyMoney: penaltyMoney.toNumber(),
        penaltyMoney1: penaltyMoney1.toNumber(),
        finalMoney,
      };
    } else {
      const penaltyMoney = new Decimal(processedValue || 0);
      const penaltyMoney1 = new Decimal(baseInfoEndRef.current.getFieldValue('penaltyMoney1') || 0);
      const finalMoney = penaltyMoney.plus(penaltyMoney1).toNumber();
      return {
        penaltyMoney: penaltyMoney.toNumber(),
        penaltyMoney1: penaltyMoney1.toNumber(),
        finalMoney,
      };
    }
  }, []);

  /**
   * 处理订单选择，填充相关字段
   * @param {string} value - 订单ID
   */
  const handleOrderSelect = useCallback(
    async (value) => {
      // 先清空相关字段
      await setFieldsValue({
        orderId: '',
        servicerType: '',
        actualSchedule: '',
        carId: '',
        carType: '',
        planStartTime: null,
        servicerId: '',
        servicerName: {
          key: '',
          label: '',
        },
        defineSupplier: {
          key: '',
          label: '',
        },
      });

      // 找到对应订单并填充数据
      const order = orderList.find((item) => item.orderId === value);

      if (order) {
        console.log('%c [ order ]: ', 'color: #bf2c9f; background: pink; font-size: 13px;', order);
        await setFieldsValue({
          orderId: order.orderId,
          servicerType: order.servicerType,
          actualSchedule: order.actualSchedule,
          carId: order.carId,
          carType: order.carType,
          planStartTime: order.planStartTime ? moment(order.planStartTime) : null,
          servicerId: order.servicerId,
          servicerName: {
            key: order.servicerId,
            label: order.servicerName,
          },
          defineSupplier: {
            key: order.defineSupplierId,
            label: order.defineSupplier,
          },
        });

        // 查找供应商
        await handleSelectSearch('servicerName', order.servicerName);
        // 查找定责供应商
        await handleSelectSearch('defineSupplier', order.defineSupplier);
      }
    },
    [orderList, setFieldsValue],
  );

  /**
   * 处理扣款类型变更
   * @param {string} value - 扣款类型值
   */
  const handleDeductTypeChange = useCallback((value) => {
    const currentErrorDescription = baseInfoEndRef.current?.getFieldValue('errorDescription') || queryInfo?.errorDescription || '';
    // 默认值
    let penaltyMoneyTextNew = '罚款金额';
    let penaltyMoney1TextNew = '罚款金额1';
    let pricingStandardNew = '定标标准';
    let actualExecutionNew = '实际执行';
    let isOnesidedLackOfVehiclesNew = false;
    let isInnerLackNew = false;
    let deductTypeAttrNew = false;

    // 设置表单字段值 - 当扣款类型改变时，清空异常子类
    setFieldsValue({
      deductType: value,
      errorDescription: undefined, // 清空异常子类
    });

    // 根据扣款类型展示罚款金额 和 罚款金额1
    console.log('%c [ 扣款类型变更 value ]: ', 'color: #bf2c9f; background: pink; font-size: 13px;', value);

    // 保存扣款类型值
    setDeductTypeValue(value);

    // 使用switch语句根据扣款类型设置相关状态
    switch (value) {
      case 'INSUFFICIENT_INTERNAL_SPACE': // 内空不足
        deductTypeAttrNew = true;
        isInnerLackNew = true;
        pricingStandardNew = '需求内空/载重';
        actualExecutionNew = '实际内空/载重';
        penaltyMoneyTextNew = '单边车型不符';
        penaltyMoney1TextNew = '内空不足';
        break;
      case 'ONESIDED_LACK_OF_VEHICLES': // 单边缺车
        isOnesidedLackOfVehiclesNew = true;
        penaltyMoneyTextNew = '单边缺车';
        penaltyMoney1TextNew = '单边缺车差额';
        setFieldsValue({
          pricingStandard: undefined, // 清空定标标准
          actualExecution: undefined, // 清空实际执行
        });
        break;
      case 'SUPPLIER_ABNORMAL_DEDUCTION': // 供应商异常扣款
        penaltyMoneyTextNew = '供应商异常扣款';
        setFieldsValue({
          pricingStandard: undefined, // 清空定标标准
          actualExecution: undefined, // 清空实际执行
        });
        break;
      case 'TIME_LIMIT_PENALTY': // 时效罚款
        penaltyMoneyTextNew = '时效罚款';
        break;
      case 'APP_UN_CHECK': // App未打卡
        penaltyMoneyTextNew = 'app未打卡';
        break;
      case 'BEHAVIORAL_FINES': // 行为罚款
        penaltyMoneyTextNew = '行为罚款';
        break;
      case 'DEDUCTION_FOR_CARGO_DAMAGE_CLAIM': // 货损理赔扣款
        penaltyMoneyTextNew = '货损理赔扣款';
        break;
      case 'OTHER': // 其他
        penaltyMoneyTextNew = '其他';
        break;
      case 'DEDUCTION_REDUCTION': // 扣款减免
        penaltyMoneyTextNew = '扣款减免';
        break;
      case 'VEHICLE_RETURN_INCENTIVE': // 压退车补贴
        penaltyMoneyTextNew = `${currentErrorDescription}补贴`;
        break;
      default:
        // 默认值已在函数开头设置
        break;
    }

    // 更新状态
    setDeductTypeAttr(deductTypeAttrNew);
    setIsOnesidedLackOfVehicles(isOnesidedLackOfVehiclesNew);
    setIsInnerLack(isInnerLackNew);
    setPenaltyMoneyText(penaltyMoneyTextNew);
    setPenaltyMoney1Text(penaltyMoney1TextNew);
    setPricingStandard(pricingStandardNew);
    setActualExecution(actualExecutionNew);
  }, [setFieldsValue]);

  /**
   * 处理异常子类变更，更新罚款金额相关文本和状态
   * @param {string} value - 选择的异常子类值
   */
  const handleErrorDescriptionChange = useCallback((value) => {
    // 获取当前扣款类型值，以便协调状态更新
    const currentDeductType = baseInfoEndRef.current?.getFieldValue('deductType') || value;
    const currentErrorDescription = value || '';
    // 默认值
    let penaltyMoneyTextNew = '罚款金额';
    let penaltyMoney1TextNew = '罚款金额1';
    let pricingStandardNew = '定标标准';
    let actualExecutionNew = '实际执行';
    let isOnesidedLackOfVehiclesNew = false;
    let isInnerLackNew = false;

    // 打印当前选择的异常子类值，便于调试
    console.log('%c [ 选择的异常子类 ]: ', 'color: #bf2c9f; background: pink; font-size: 13px;', value);
    console.log('%c [ 异常子类参数类型 ]: ', 'color: #bf2c9f; background: pink; font-size: 13px;', typeof value);
    console.log('%c [ 当前扣款类型 ]: ', 'color: #bf2c9f; background: pink; font-size: 13px;', currentDeductType);

    // 如果value是函数，记录并使用默认值
    if (typeof value === 'function') {
      console.error('异常：value参数是函数而不是字符串', value);

      // 更新表单字段值 - 使用空字符串作为回退值
      setFieldsValue({
        errorDescription: '',
      });

      // 更新状态 - 使用默认值
      setPenaltyMoneyText(penaltyMoneyTextNew);
      setPenaltyMoney1Text(penaltyMoney1TextNew);
      setPricingStandard(pricingStandardNew);
      setActualExecution(actualExecutionNew);
      setIsOnesidedLackOfVehicles(isOnesidedLackOfVehiclesNew);
      setIsInnerLack(isInnerLackNew);

      return;
    }

    // 首先基于扣款类型设置初始值
    switch (currentDeductType) {
      case 'INSUFFICIENT_INTERNAL_SPACE': // 内空不足
        pricingStandardNew = '需求内空/载重';
        actualExecutionNew = '实际内空/载重';
        penaltyMoneyTextNew = '单边车型不符';
        penaltyMoney1TextNew = '内空不足';
        isInnerLackNew = true;
        break;
      case 'ONESIDED_LACK_OF_VEHICLES': // 单边缺车
        penaltyMoneyTextNew = '单边缺车';
        penaltyMoney1TextNew = '单边缺车差额';
        pricingStandardNew = '定标车型';
        actualExecutionNew = '实际车型';
        isOnesidedLackOfVehiclesNew = true;
        setFieldsValue({
          pricingStandard: undefined, // 清空定标标准
          actualExecution: undefined, // 清空实际执行
        });
        break;
      case 'SUPPLIER_ABNORMAL_DEDUCTION': // 供应商异常扣款
        penaltyMoneyTextNew = '供应商异常扣款';
        setFieldsValue({
          pricingStandard: undefined, // 清空定标标准
          actualExecution: undefined, // 清空实际执行
        });
        break;
      case 'TIME_LIMIT_PENALTY': // 时效罚款
        penaltyMoneyTextNew = '时效罚款';
        break;
      case 'APP_UN_CHECK': // App未打卡
        penaltyMoneyTextNew = 'app未打卡';
        break;
      case 'BEHAVIORAL_FINES': // 行为罚款
        penaltyMoneyTextNew = '行为罚款';
        break;
      case 'DEDUCTION_FOR_CARGO_DAMAGE_CLAIM': // 货损理赔扣款
        penaltyMoneyTextNew = '货损理赔扣款';
        break;
      case 'OTHER': // 其他
        penaltyMoneyTextNew = '其他';
        break;
      case 'DEDUCTION_REDUCTION': // 扣款减免
        penaltyMoneyTextNew = '扣款减免';
        break;
      case 'VEHICLE_RETURN_INCENTIVE': // 压退车补贴
        penaltyMoneyTextNew = `${currentErrorDescription}补贴`;
        break;
      default:
        // 默认值已在函数开头设置
        break;
    }

    // 然后基于异常子类进一步精确设置
    switch (value) {
      case '单边缺车':
        penaltyMoneyTextNew = '单边缺车';
        penaltyMoney1TextNew = '单边缺车差额';
        isOnesidedLackOfVehiclesNew = true;
        isInnerLackNew = false;
        setFieldsValue({
          pricingStandard: undefined, // 清空定标标准
          actualExecution: undefined, // 清空实际执行
        });
        break;
      case '内空不足':
        penaltyMoneyTextNew = '单边车型不符';
        penaltyMoney1TextNew = '内空不足';
        pricingStandardNew = '需求内空/载重';
        actualExecutionNew = '实际内空/载重';
        isOnesidedLackOfVehiclesNew = false;
        isInnerLackNew = true;
        break;
      case '堵门':
      case '扣货':
      case '虚假信息':
        // 如果扣款类型是"供应商异常扣款"，保持该扣款类型的标签
        if (currentDeductType !== 'SUPPLIER_ABNORMAL_DEDUCTION') {
          penaltyMoneyTextNew = '供应商异常扣款';

        }
        penaltyMoney1TextNew = '罚款金额1';
        pricingStandardNew = '定标标准';
        actualExecutionNew = '实际执行';
        isOnesidedLackOfVehiclesNew = false;
        isInnerLackNew = false;
        break;
      case '压车':
        // 如果扣款类型是"压退车补贴"，保持该扣款类型的标签
        if (currentDeductType === 'VEHICLE_RETURN_INCENTIVE') {
          penaltyMoneyTextNew = '压车补贴';
        }
        penaltyMoney1TextNew = '罚款金额1';
        pricingStandardNew = '定标标准';
        actualExecutionNew = '实际执行';
        isOnesidedLackOfVehiclesNew = false;
        isInnerLackNew = false;
        break;
      case '退车':
        // 如果扣款类型是"压退车补贴"，保持该扣款类型的标签
        if (currentDeductType === 'VEHICLE_RETURN_INCENTIVE') {
          penaltyMoneyTextNew = '退车补贴';
        }
        pricingStandardNew = '定标标准';
        actualExecutionNew = '实际执行';
        penaltyMoney1TextNew = '罚款金额1';
        isOnesidedLackOfVehiclesNew = false;
        isInnerLackNew = false;
        break;
      case 'APP未打卡':
        // 如果扣款类型是"APP_UN_CHECK"，保持该扣款类型的标签
        if (currentDeductType !== 'APP_UN_CHECK') {
          penaltyMoneyTextNew = 'app未打卡';
        }
        pricingStandardNew = '定标标准';
        actualExecutionNew = '实际执行';
        penaltyMoney1TextNew = '罚款金额1';
        isOnesidedLackOfVehiclesNew = false;
        isInnerLackNew = false;
        break;
      case '时效罚款':
        // 如果扣款类型是"TIME_LIMIT_PENALTY"，保持该扣款类型的标签
        if (currentDeductType !== 'TIME_LIMIT_PENALTY') {
          penaltyMoneyTextNew = '时效罚款';
        }
        pricingStandardNew = '定标标准';
        actualExecutionNew = '实际执行';
        penaltyMoney1TextNew = '罚款金额1';
        isOnesidedLackOfVehiclesNew = false;
        isInnerLackNew = false;
        break;
      case '行为罚款':
        // 如果扣款类型是"BEHAVIORAL_FINES"，保持该扣款类型的标签
        if (currentDeductType !== 'BEHAVIORAL_FINES') {
          penaltyMoneyTextNew = '行为罚款';
        }
        pricingStandardNew = '定标标准';
        actualExecutionNew = '实际执行';
        penaltyMoney1TextNew = '罚款金额1';
        isOnesidedLackOfVehiclesNew = false;
        isInnerLackNew = false;
        break;
      case '货损理赔':
        // 如果扣款类型是"DEDUCTION_FOR_CARGO_DAMAGE_CLAIM"，保持该扣款类型的标签
        if (currentDeductType !== 'DEDUCTION_FOR_CARGO_DAMAGE_CLAIM') {
          penaltyMoneyTextNew = '货损理赔扣款';
        }
        pricingStandardNew = '定标标准';
        actualExecutionNew = '实际执行';
        penaltyMoney1TextNew = '罚款金额1';
        isOnesidedLackOfVehiclesNew = false;
        isInnerLackNew = false;
        break;
      case '其他':
        // 如果扣款类型是"OTHER"，保持该扣款类型的标签
        if (currentDeductType !== 'OTHER') {
          penaltyMoneyTextNew = '其他';
        }
        pricingStandardNew = '定标标准';
        actualExecutionNew = '实际执行';
        penaltyMoney1TextNew = '罚款金额1';
        isOnesidedLackOfVehiclesNew = false;
        isInnerLackNew = false;
        break;
      case '扣款减免':
        // 如果扣款类型是"DEDUCTION_REDUCTION"，保持该扣款类型的标签
        if (currentDeductType !== 'DEDUCTION_REDUCTION') {
          penaltyMoneyTextNew = '扣款减免';
        }
        pricingStandardNew = '定标标准';
        actualExecutionNew = '实际执行';
        penaltyMoney1TextNew = '罚款金额1';
        isOnesidedLackOfVehiclesNew = false;
        isInnerLackNew = false;
        break;
      default:
        // 如果异常子类没有特殊处理，就保留基于扣款类型设置的值
        break;
    }

    // 更新表单字段值
    setFieldsValue({
      errorDescription: value,
    });

    // 更新状态
    setPenaltyMoneyText(penaltyMoneyTextNew);
    setPenaltyMoney1Text(penaltyMoney1TextNew);
    setPricingStandard(pricingStandardNew);
    setActualExecution(actualExecutionNew);
    setIsOnesidedLackOfVehicles(isOnesidedLackOfVehiclesNew);
    setIsInnerLack(isInnerLackNew);
  }, [setFieldsValue, deductTypeValue]);

  /**
   * 处理表单字段绑定和联动
   */
  const handleBindForm = useCallback(
    (type, value) => {
      switch (type) {
        case 'servicerName': {
          if (value) {
            console.log('%c [ value ]: ', 'color: #bf2c9f; background: pink; font-size: 13px;', value);
            setFieldsValue({
              servicerName: value,
              servicerType: value?.label === '临调' ? '临调' : '供应商',
            });
          }
          else
            setFieldsValue({
              servicerName: undefined,
              servicerType: undefined,
            });
          break;
        }
        case 'defineSupplier': {
          if (value) {
            setFieldsValue({
              defineSupplier: value,
              defineSupplierType: value?.label.includes('公司') ? {
                label: '供应商',
                value: '供应商',
              } : {
                label: '临调',
                value: '临调',
              },
            });
          }
          else {
            setFieldsValue({
              defineSupplier: undefined,
              defineSupplierType: undefined,
            });
          }
          break;
        }
        case 'penaltyType':
          // 清空扣款类型
          setFieldsValue({
            penaltyType: value,
            deductType: null,
          });
          console.log('%c [ value ]: ', 'color: #bf2c9f; background: pink; font-size: 13px;', value);
          if (value.value === 'DEDUCT') {
            setIsDeduct(true);
          } else {
            setIsDeduct(false);
          }
          break;
        case 'orderId':
          handleOrderSelect(value);
          break;
        // case 'penaltyMoney':
        // case 'penaltyMoney1': {
        //   const finalMoney = calculateFinalMoney(type, value);
        //   setFieldsValue({
        //     ...finalMoney,
        //   });
        //   break;
        // }
        case 'deductType': {

          // 检查value是否为函数类型，避免传递错误类型参数
          if (typeof value === 'function') {
            console.error('处理异常子类时收到函数类型参数，将使用空字符串代替', value);
            handleDeductTypeChange('');
          } else {
            handleDeductTypeChange(value);
          }
          break;
        }
        case 'errorDescription': {
          // 检查value是否为函数类型，避免传递错误类型参数
          if (typeof value === 'function') {
            console.error('处理异常子类时收到函数类型参数，将使用空字符串代替', value);
            handleErrorDescriptionChange('');
          } else {
            handleErrorDescriptionChange(value);
          }
          break;
        }
        default:
          break;
      }
    },
    [allCarrierList, setFieldsValue, handleOrderSelect, handleErrorDescriptionChange, handleDeductTypeChange],
  );

  // 表单提交
  const save = async () => {
    try {
      const baseInfo = await asyncAwaitForm(baseInfoEndRef.current);
      console.log('%c [ baseInfo ]: ', 'color: #bf2c9f; background: pink; font-size: 13px;', baseInfo);
      if (!baseInfo) {
        message.warning('请先填写必填项');
        return;
      }
      // 处理日期字段，将moment对象转换为指定格式字符串
      const formattedData = {
        ...queryInfo,
        ...baseInfo,
        servicerId: baseInfo.servicerName?.key,
        servicerName: baseInfo.servicerName?.label,
        defineSupplierId: baseInfo.defineSupplier?.key,
        defineSupplier: baseInfo.defineSupplier?.label,
      };
      const dateFields = ['happenTime', 'planStartTime'];

      dateFields.forEach((field) => {
        if (formattedData[field] && moment.isMoment(formattedData[field])) {
          formattedData[field] = formattedData[field].format('YYYY-MM-DD HH:mm:ss');
        }
      });
      const payload = {
        // ...queryInfo,
        ...formattedData,
        defineSupplierType: baseInfo.defineSupplierType.label,
        photos: getUids(baseInfo?.photos) || [],
      };

      delete payload.auditStatus;
      delete payload.updateCode;
      delete payload.updateName;
      delete payload.updateTime;
      dispatch({
        type: 'NonDirectFines/insertOrUpdate',
        payload,
        callback: () => {
          onClose();
          refreshPage?.(); // 刷新页面
        },
      });
    } catch (error) {
      console.error('表单校验失败:', error);
      message.error('请检查表单填写是否完整');
    }
  };

  // 处理操作记录数据，确保能正确处理各种格式
  const processOperationRecords = useMemo(() => {
    if (!queryInfo?.operationRecord) return [];

    console.log('处理操作记录数据:', queryInfo.operationRecord);

    // 检查是否是数组
    if (Array.isArray(queryInfo.operationRecord)) {
      return queryInfo.operationRecord;
    }

    // 如果是对象而不是数组，尝试将其转换为数组
    if (typeof queryInfo.operationRecord === 'object') {
      const records = [];
      // 遍历对象的所有键
      Object.keys(queryInfo.operationRecord).forEach(key => {
        // 如果键是数字或能转换为数字，将值添加到数组中
        if (!isNaN(Number(key))) {
          records.push(queryInfo.operationRecord[key]);
        } else if (key !== 'length') { // 排除length属性
          // 如果键不是数字也不是length，将该项也作为记录添加
          const record = queryInfo.operationRecord[key];
          if (typeof record === 'object') {
            records.push(record);
          }
        }
      });
      return records;
    }

    return [];
  }, [queryInfo]);

  // 自定义操作记录组件，不使用 Timeline
  const renderOperationRecords = () => {
    if (!queryInfo?.operationRecord ||
      (Array.isArray(queryInfo.operationRecord) && queryInfo.operationRecord.length === 0)) {
      return null;
    }

    // 尝试获取操作记录
    let records = [];

    // 首先尝试获取数组形式的记录
    if (Array.isArray(queryInfo.operationRecord)) {
      records = queryInfo.operationRecord;
    } else if (typeof queryInfo.operationRecord === 'object') {
      // 处理对象形式的记录
      Object.keys(queryInfo.operationRecord).forEach(key => {
        if (key !== 'length' && typeof queryInfo.operationRecord[key] === 'object') {
          records.push(queryInfo.operationRecord[key]);
        }
      });
    }

    if (records.length === 0) return null;

    // 获取当前扣款类型和异常子类
    const currentDeductType = queryInfo?.deductType?.name;
    const currentErrorDescription = queryInfo?.errorDescription;

    // 动态生成罚款金额和罚款金额1的列标题
    let penaltyMoneyTitle = '罚款金额';
    let penaltyMoney1Title = '罚款金额1';

    // 根据扣款类型和异常子类设置标题，与handleDeductTypeChange和handleErrorDescriptionChange保持一致
    if (isOnesidedLackOfVehicles || currentDeductType === 'ONESIDED_LACK_OF_VEHICLES' || currentErrorDescription === '单边缺车') {
      penaltyMoneyTitle = '单边缺车';
      penaltyMoney1Title = '单边缺车差额';
    } else if (isInnerLack || currentDeductType === 'INSUFFICIENT_INTERNAL_SPACE' || currentErrorDescription === '内空不足') {
      penaltyMoneyTitle = '单边车型不符';
      penaltyMoney1Title = '内空不足';
    } else {
      // 根据扣款类型设置标题
      switch (currentDeductType) {
        case 'SUPPLIER_ABNORMAL_DEDUCTION': // 供应商异常扣款
          penaltyMoneyTitle = '供应商异常扣款';
          break;
        case 'TIME_LIMIT_PENALTY': // 时效罚款
          penaltyMoneyTitle = '时效罚款';
          break;
        case 'APP_UN_CHECK': // App未打卡
          penaltyMoneyTitle = 'app未打卡';
          break;
        case 'BEHAVIORAL_FINES': // 行为罚款
          penaltyMoneyTitle = '行为罚款';
          break;
        case 'DEDUCTION_FOR_CARGO_DAMAGE_CLAIM': // 货损理赔扣款
          penaltyMoneyTitle = '货损理赔扣款';
          break;
        case 'OTHER': // 其他
          penaltyMoneyTitle = '其他';
          break;
        case 'DEDUCTION_REDUCTION': // 扣款减免
          penaltyMoneyTitle = '扣款减免';
          break;
        case 'VEHICLE_RETURN_INCENTIVE': // 压退车补贴
          penaltyMoneyTitle = `${currentErrorDescription}补贴`;
          break;
        default:
          // 使用默认值
          break;
      }
    }

    // 为标题添加"金额"字样（如果不包含）
    const formattedPenaltyMoneyTitle = penaltyMoneyTitle.includes('金额') ?
      penaltyMoneyTitle : penaltyMoneyTitle + '金额';

    const formattedPenaltyMoney1Title = penaltyMoney1Title.includes('金额') ?
      penaltyMoney1Title : penaltyMoney1Title + '金额';
    const findCategoryTextByName = (name) => {
      return CategoryData?.['75300']?.find((item) => item.name === name)?.value;
    };
    return (
      <>
        <Divider style={{ margin: '24px 0 16px' }} />
        <div style={{ marginBottom: '24px' }}>
          <h3 style={{ fontWeight: 'bold', marginBottom: '16px' }}>操作记录</h3>
          <div style={{ marginLeft: '45px', position: 'relative' }}>
            {/* 主时间线，贯穿整个记录列表 */}
            <div style={{
              position: 'absolute',
              left: '-22px',
              top: '8px',
              width: '2px',
              height: 'calc(100% - 8px)',
              background: '#FF9500',
              zIndex: 0
            }}></div>

            {records.map((record, index) => {
              // 获取记录信息
              const title = record.title || '';
              const updateTime = record.updateTime ? moment(record.updateTime).format('YYYY-MM-DD HH:mm:ss') : '';
              const updateName = record.updateName || '';

              // 设置操作状态和颜色
              let color = '#FF9500'; // 默认橙色
              let status = findCategoryTextByName(record.auditStatus);
              // 确定最后一列的标题和内容
              const lastColumnTitle = title.includes('审核') ? '审核原因' : '备注';
              const lastColumnContent = title.includes('审核') ? (record.auditReason || '') : (record.remark || '');

              return (
                <div key={index} style={{ marginBottom: index < records.length - 1 ? '45px' : '0', position: 'relative' }}>
                  {/* 节点圆点 */}
                  <div style={{
                    position: 'absolute',
                    left: '-27px',
                    top: '4px',
                    width: '10px',
                    height: '10px',
                    borderRadius: '50%',
                    background: color,
                    border: '2px solid #fff',
                    boxShadow: '0 0 0 1px ' + color,
                    zIndex: 2
                  }}></div>

                  {/* 标题和内容 */}
                  <div>
                    <div style={{
                      marginBottom: '12px',
                      fontSize: '14px',
                      color: '#333',
                      fontWeight: title.includes('上报') ? 'bold' : 'normal'
                    }}>
                      <span style={{ color: color, fontWeight: 'bold' }}>{title}</span> （操作人：{updateName}，操作时间：{updateTime}）
                    </div>

                    {/* 表格 */}
                    <div style={{ borderRadius: '2px', overflow: 'hidden' }}>
                      <table style={{ width: '100%', borderCollapse: 'collapse', border: '1px solid #e8e8e8' }}>
                        <thead>
                          <tr style={{ backgroundColor: '#f5f5f5' }}>
                            <th style={{ padding: '8px 16px', textAlign: 'center', borderRight: '1px solid #e8e8e8', width: '20%', fontWeight: 'normal' }}>定责供应商</th>
                            <th style={{ padding: '8px 16px', textAlign: 'center', borderRight: '1px solid #e8e8e8', width: '20%', fontWeight: 'normal' }}>定责供应商类型</th>
                            <th style={{ padding: '8px 16px', textAlign: 'center', borderRight: '1px solid #e8e8e8', width: '20%', fontWeight: 'normal' }}>{formattedPenaltyMoneyTitle}</th>
                            <th style={{ padding: '8px 16px', textAlign: 'center', borderRight: '1px solid #e8e8e8', width: '20%', fontWeight: 'normal' }}>{formattedPenaltyMoney1Title}</th>
                            <th style={{ padding: '8px 16px', textAlign: 'center', borderRight: '1px solid #e8e8e8', width: '10%', fontWeight: 'normal' }}>处理状态</th>
                            <th style={{ padding: '8px 16px', textAlign: 'center', width: '10%', fontWeight: 'normal' }}>
                              {lastColumnTitle}
                            </th>
                          </tr>
                        </thead>
                        <tbody>
                          <tr>
                            <td style={{ padding: '8px 16px', textAlign: 'center', borderRight: '1px solid #e8e8e8', borderTop: '1px solid #e8e8e8' }}>{record.defineSupplier || ''}</td>
                            <td style={{ padding: '8px 16px', textAlign: 'center', borderRight: '1px solid #e8e8e8', borderTop: '1px solid #e8e8e8' }}>{record.defineSupplierType || ''}</td>
                            <td style={{ padding: '8px 16px', textAlign: 'center', borderRight: '1px solid #e8e8e8', borderTop: '1px solid #e8e8e8' }}>
                              {record.penaltyMoney !== undefined && record.penaltyMoney !== null ? record.penaltyMoney : ''}
                            </td>
                            <td style={{ padding: '8px 16px', textAlign: 'center', borderRight: '1px solid #e8e8e8', borderTop: '1px solid #e8e8e8' }}>
                              {record.penaltyMoney1 !== undefined && record.penaltyMoney1 !== null ? record.penaltyMoney1 : ''}
                            </td>
                            <td style={{ padding: '8px 16px', textAlign: 'center', borderRight: '1px solid #e8e8e8', borderTop: '1px solid #e8e8e8' }}>
                              {status}
                            </td>
                            <td style={{ padding: '8px 16px', textAlign: 'center', borderTop: '1px solid #e8e8e8' }}>
                              {lastColumnContent}
                            </td>
                          </tr>
                        </tbody>
                      </table>
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      </>
    );
  };

  // 使用 useMemo 计算表单数据源
  const formDataSource = useMemo(() => {
    // console.log('%c [ isOnesidedLackOfVehicles ]: ', 'color: #bf2c9f; background: pink; font-size: 13px;', isOnesidedLackOfVehicles);
    // console.log('%c [ isInnerLack ]: ', 'color: #bf2c9f; background: pink; font-size: 13px;', isInnerLack);
    // console.log('%c [ deductTypeValue ]: ', 'color: #bf2c9f; background: pink; font-size: 13px;', deductTypeValue);

    if (queryInfo?.deductType?.name === 'ONESIDED_LACK_OF_VEHICLES') {
      setIsOnesidedLackOfVehicles(true);
    } else {
      setIsOnesidedLackOfVehicles(false);
    }
    if (queryInfo?.deductType?.name === 'INSUFFICIENT_INTERNAL_SPACE') {
      setIsInnerLack(true);
    } else {
      setIsInnerLack(false);
    }

    // 参数顺序必须与baseItem函数定义一致
    return baseItem(
      queryInfo,                // data
      isOnesidedLackOfVehicles, // isOnesidedLackOfVehicles
      isInnerLack,              // isInnerLack
      {                         // attributes
        dataStatus: {
          options: renderOptions(CategoryData, 23000),
        },
        penaltyType: {
          options: renderOptions(CategoryData, 99100),
        },
        deductType: {
          options: renderOptions(CategoryData, 99101),
        },
        deductType2: {
          options: renderOptions(CategoryData, 99102),
        },
        servicerName: {
          options: allCarrierList.map((item) => ({
            value: item.id,
            label: item.carrierName,
          })),
        },
        defineSupplier: {
          options: allCarrierList.map((item) => ({
            value: item.id,
            label: item.carrierName,
          })),
        },
        carId: {
          options: selectOutCarList.map((item) => ({
            value: item.id,
            label: item.id,
          })),
        },
        orderId: {
          options: orderList.map((item) => ({
            ...item,
            value: item.orderId,
            label: item.orderId,
          })),
        },
      },
      handleSelectSearch,       // handleSelectChange
      handleBindForm,           // handleBindForm
      isView,                   // isView
      isDeduct,                 // isDeduct
      deductTypeAttr,           // deductTypeAttr
      deductTypeValue,          // deductTypeValue
      null,                     // errorDescriptionOptions
      pricingStandard,          // pricingStandard
      actualExecution,          // actualExecution
      penaltyMoneyText,         // penaltyMoneyText
      penaltyMoney1Text,        // penaltyMoney1Text
    );
  }, [
    queryInfo,
    CategoryData,
    selectOutCarList,
    allCarrierList,
    handleSelectSearch,
    orderList,
    handleBindForm,
    isView,
    isDeduct,
    deductTypeAttr,
    deductTypeValue,
    isOnesidedLackOfVehicles,
    isInnerLack,
    pricingStandard,
    actualExecution,
    penaltyMoneyText,
    penaltyMoney1Text,
  ]);

  return (
    <Drawer
      title="上报"
      destroyOnClose
      visible={drawerVisible}
      onClose={onClose}
      width="70%"
      bodyStyle={{ padding: '10px 10px 45px 10px' }}
    >
      <FormContent ref={baseInfoEndRef} isView={isView} colspan={3} dataSource={formDataSource} />

      {isView && renderOperationRecords()}

      <DrawerFooter>
        <Button onClick={onClose} style={{ marginRight: 10 }}>
          取消
        </Button>
        {!isView && (
          <Button
            type="primary"
            loading={loading.effects['NonDirectFines/insertOrUpdate']}
            onClick={save}
            style={{ marginRight: 10 }}
          >
            保存
          </Button>
        )}
      </DrawerFooter>
    </Drawer>
  );
};

export default connect(({ NonDirectFines, carrierVehicle, CommonInterface, loading }) => ({
  ...NonDirectFines,
  carrierVehicle,
  commonInterface: CommonInterface,
  loading,
}))(DetailTables);
