import moment from 'moment';
import CarTypeSelect from '@/components/business/carTypeSelect';

// 扣款类型与异常子类映射配置
const DEDUCT_TYPE_ERROR_MAPPING = {
  单边缺车: [{ label: '单边缺车', value: '单边缺车' }],
  内空不足: [{ label: '内空不足', value: '内空不足' }],
  供应商异常扣款: [
    { label: '堵门', value: '堵门' },
    { label: '扣货', value: '扣货' },
    { label: '虚假信息', value: '虚假信息' },
  ],
  压退车补贴: [
    { label: '压车', value: '压车' },
    { label: '退车', value: '退车' },
  ],
  // 添加APP未打卡映射
  app未打卡: [],
  // 添加时效罚款映射
  时效罚款: [],
  // 添加行为罚款映射
  行为罚款: [],
  // 添加货损理赔扣款映射
  货损理赔扣款: [],
  // 添加其他映射
  其他: [],
  // 添加扣款减免映射
  扣款减免: [],
};

/**
 * 根据扣款类型获取对应的异常子类选项
 * @param {string} deductTypeName 扣款类型名称
 * @param {Array} deductTypeList 扣款类型列表
 * @returns {Array} 异常子类选项数组
 */
const getErrorDescriptionOptions = (deductTypeName, deductTypeList = []) => {
  // 安全检查：确保deductTypeList是数组
  if (!Array.isArray(deductTypeList)) {
    // console.warn('getErrorDescriptionOptions deductTypeList不是数组，将使用默认值');
    deductTypeList = [];
  }

  // 扣款类型常量与中文映射
  const DEDUCT_TYPE_NAME_MAP = {
    ONESIDED_LACK_OF_VEHICLES: '单边缺车',
    INSUFFICIENT_INTERNAL_SPACE: '内空不足',
    SUPPLIER_ABNORMAL_DEDUCTION: '供应商异常扣款',
    VEHICLE_RETURN_INCENTIVE: '压退车补贴',
    APP_UN_CHECK: 'app未打卡',
    TIME_LIMIT_PENALTY: '时效罚款',
    BEHAVIORAL_FINES: '行为罚款',
    DEDUCTION_FOR_CARGO_DAMAGE_CLAIM: '货损理赔扣款',
    OTHER: '其他',
    DEDUCTION_REDUCTION: '扣款减免',
  };

  // 扣款类型
  let deductType = {};
  try {
    deductType = deductTypeList.find(
      (item) => (item && item.value === deductTypeName) || item.label === deductTypeName,
    );
  } catch (error) {
    // console.error('getErrorDescriptionOptions 查找扣款类型出错:', error);
    deductType = {
      label: deductTypeName,
      value: deductTypeName,
    }; // 降级处理：直接使用传入的名称
  }

  // console.log(
  //   '%c [ getErrorDescriptionOptions deductTypeName ]: ',
  //   'color: #bf2c9f; background: pink; font-size: 13px;',
  //   deductTypeName,
  //   deductType,
  // );

  // 尝试多种方式获取映射的中文名称
  const chineseName = DEDUCT_TYPE_NAME_MAP[deductTypeName] || deductType?.label || deductTypeName;

  // console.log('%c [ 尝试获取的中文名称 ]: ', 'color: #bf2c9f; background: pink; font-size: 13px;', chineseName);

  // 如果通过value找不到对应的label，则直接使用deductTypeName作为key
  const options = DEDUCT_TYPE_ERROR_MAPPING[chineseName] ||
    DEDUCT_TYPE_ERROR_MAPPING[deductType?.label] ||
    DEDUCT_TYPE_ERROR_MAPPING[deductTypeName] || [{ label: chineseName, value: chineseName }]; // 如果上述都找不到，根据映射表的键值类型创建默认选项

  // console.log(
  //   '%c [ getErrorDescriptionOptions options ]: ',
  //   'color: #bf2c9f; background: pink; font-size: 13px;',
  //   options,
  // );
  return options;
};

/**
 * 基础表单项
 * @param {*} data
 * @param {*} attributes
 * @param {*} handleSelectChange
 * @param {*} handleBindForm
 * @param {*} isView
 * @param {*} isDeduct
 * @param {*} deductTypeAttr
 * @param {*} errorDescriptionOptions - 异常子类选项
 * @returns
 */
function baseItem(
  data,
  isOnesidedLackOfVehicles,
  isInnerLack,
  attributes,
  handleSelectChange,
  handleBindForm,
  isView,
  isDeduct,
  deductTypeAttr,
  deductTypeValue,
  errorDescriptionOptions,
  pricingStandard,
  actualExecution,
  penaltyMoneyText,
  penaltyMoney1Text,
) {
  // 扣款类型合集
  const deductTypeList = [...(attributes.deductType?.options || []), ...(attributes.deductType2?.options || [])];
  // 获取当前扣款类型对应的异常子类选项
  const getCurrentErrorDescriptionOptions = () => {
    // 优先使用传入的选项，如果没有则根据当前扣款类型获取
    if (errorDescriptionOptions && errorDescriptionOptions.length > 0) {
      return errorDescriptionOptions;
    }
    const currentDeductType = deductTypeValue || data.deductType?.name || data.deductType2?.name || data.deductType;
    // console.log(
    //   '%c [ getCurrentErrorDescriptionOptions currentDeductType ]: ',
    //   'color: #bf2c9f; background: pink; font-size: 13px;',
    //   currentDeductType,
    // );
    return getErrorDescriptionOptions(currentDeductType, deductTypeList);
  };

  return [
    {
      label: '发生时间',
      name: 'happenTime',
      dateFormat: 'YYYY-MM-DD HH:mm:ss',
      initialValue: data.happenTime ? moment(data.happenTime) : null,
      rules: [{ required: true, message: '请选择发生时间' }],
      type: 'datePicker',
      attributes: {
        showTime: true,
        format: 'YYYY-MM-DD HH:mm:ss',
      },
    },
    {
      label: '订单号',
      name: 'orderId',
      type: 'select',
      rules: [{ required: true }],
      initialValue: data.orderId,
      attributes: {
        onChange: (val) => {
          handleBindForm('orderId', val);
        },
        onSearch: (val) => {
          handleSelectChange('orderId', val);
        },
        allowClear: true,
        showSearch: true,
        filterOption: false, // 解决options 搜索后不更新问题
      },
      options: attributes.orderId && attributes.orderId.options,
    },
    {
      label: '车牌号',
      name: 'carId',
      initialValue: data.carId,
      rules: [{ required: false }],
      attributes: {
        onChange: (val) => {
          handleBindForm('carId', val);
        },
        onSearch: (val) => {
          handleSelectChange('carId', val);
        },
        allowClear: true,
        showSearch: true,
        filterOption: false, // 解决options 搜索后不更新问题
      },
      type: 'select',
      options: attributes.carId && attributes.carId.options,
    },
    {
      label: '计划出发时间',
      name: 'planStartTime',
      initialValue: data.planStartTime ? moment(data.planStartTime) : null,
      type: 'datePicker',
      dateFormat: 'YYYY-MM-DD HH:mm:ss',
      attributes: { showTime: true, format: 'YYYY-MM-DD HH:mm:ss' },
      disabled: !!data?.planStartTime,
    },
    {
      label: '供应商',
      name: 'servicerName',
      rules: [{ required: false, message: '请选择供应商' }],
      attributes: {
        onChange: (val) => {
          handleBindForm('servicerName', val);
        },
        onSearch: (val) => {
          handleSelectChange('servicerName', val);
        },
        allowClear: true,
        showSearch: true,
        filterOption: false, // 解决options 搜索后不更新问题
        labelInValue: true,
      },
      type: 'select',
      options: attributes.servicerName && attributes.servicerName.options,
      initialValue: data.servicerId ? { key: data.servicerId, label: data.servicerName } : undefined,
    },
    {
      label: '供应商类型',
      name: 'servicerType',
      initialValue: data.servicerType,
      rules: [{ required: false, message: '请选择供应商类型' }],
      attributes: { disabled: true },
    },
    {
      label: '实际线路',
      name: 'actualSchedule',
      attributes: {
        maxLength: 200,
      },
      initialValue: data.actualSchedule,
    },
    {
      type: 'render',
      label: '实际车型',
      name: 'carType',
      initialValue: data.carType,
      render: isView ? (
        data?.carType
      ) : (
        <CarTypeSelect
          placeholder="请选择实际车型"
          keyMap={{ label: 'carType', value: 'carType' }}
          fetch={{
            manual: false,
          }}
        />
      ),
    },
    {
      label: '备注',
      name: 'remark',
      attributes: {
        maxLength: 200,
      },
      initialValue: data.remark,
    },
    {
      label: '奖罚类型',
      name: 'penaltyType',
      type: 'radio',
      initialValue: data.penaltyType && data.penaltyType.name,
      rules: [
        {
          required: true,
          message: '请选择奖罚类型',
        },
      ],
      attributes: {
        onChange: (val) => {
          console.log('%c [ val ]: ', 'color: #bf2c9f; background: pink; font-size: 13px;', val);
          // 从事件对象中提取value
          const value = val.target ? val.target.value : val;
          const option = attributes.penaltyType && attributes.penaltyType.options.find((item) => item.value === value);
          handleBindForm('penaltyType', option);
          handleBindForm('errorDescription', undefined);
        },
      },
      radioOptions: attributes.penaltyType && attributes.penaltyType.options,
    },
    {
      label: '扣款类型',
      name: 'deductType',
      required: true,
      initialValue: data.deductType && isView ? data?.deductType?.name : data?.deductType?.name,
      type: 'select',
      rules: [{ required: true, message: '请选择扣款类型' }],
      options: isDeduct
        ? attributes.deductType && attributes.deductType.options
        : attributes.deductType2 && attributes.deductType2.options,
      attributes: {
        onChange: (val) => {
          handleBindForm('deductType', val);
          // 当扣款类型改变时，清空异常子类的值
          handleBindForm('errorDescription', undefined);
        },
        filterOption: false, // 解决options 搜索后不更新问题
      },
    },
    {
      label: '异常子类',
      name: 'errorDescription',
      initialValue: data.errorDescription,
      type: getCurrentErrorDescriptionOptions()?.length > 0 ? 'select' : 'input',
      options: getCurrentErrorDescriptionOptions(),
      rules: [{ required: true, message: '请选择/输入异常子类' }],
      attributes: {
        placeholder: '请选择异常子类',
        allowClear: true,
        showSearch: true,
        filterOption: (input, option) => option?.label?.toLowerCase().indexOf(input.toLowerCase()) >= 0,
        onChange: (val) => {
          handleBindForm('errorDescription', val);
        },
      },
    },
    {
      label: '异常原因',
      name: 'errorReason',
      attributes: {
        maxLength: 200,
      },
      initialValue: data.errorReason,
      rules: [{ required: true, message: '请输入异常原因' }],
    },

    {
      label: '定责供应商',
      name: 'defineSupplier',
      rules: [{ required: true, message: '请选择定责供应商' }],
      attributes: {
        onChange: (val) => {
          handleBindForm('defineSupplier', val);
        },
        onSearch: (val) => {
          handleSelectChange('defineSupplier', val);
        },
        allowClear: true,
        showSearch: true,
        filterOption: false, // 解决options 搜索后不更新问题
        labelInValue: true,
      },
      type: 'select',
      options: attributes.defineSupplier && attributes.defineSupplier.options,
      initialValue: data.defineSupplierId ? { key: data.defineSupplierId, label: data.defineSupplier } : undefined,
    },
    {
      label: '定责供应商类型',
      name: 'defineSupplierType',
      type: 'select',
      attributes: {
        onChange: (val) => {
          handleBindForm('defineSupplierType', val);
        },
        filterOption: false, // 解决options 搜索后不更新问题
        labelInValue: true,
      },
      initialValue: data.defineSupplierType
        ? { key: data.defineSupplierType, label: data.defineSupplierType }
        : undefined,
      options: [
        { label: '供应商', value: '供应商' },
        { label: '临调', value: '临调' },
      ],
      rules: [{ required: true, message: '请选择定责供应商类型' }],
    },
    {
      label: pricingStandard, // 定价标准
      name: 'calibrationStandard',
      initialValue: data.calibrationStandard,
      rules: [
        {
          required:
            !!(penaltyMoneyText?.includes('单边缺车') || deductTypeValue === 'INSUFFICIENT_INTERNAL_SPACE'),
          message: `请输入${pricingStandard}`,
        },
      ],
      attributes: {
        maxLength: 200,
        placeholder: `请输入${pricingStandard}`,
      },
    },
    {
      label: actualExecution, // 实际执行
      name: 'actualExecution',
      initialValue: data.actualExecution,
      rules: [
        {
          required:
            !!(penaltyMoneyText?.includes('单边缺车') || deductTypeValue === 'INSUFFICIENT_INTERNAL_SPACE'),
          message: `请输入${actualExecution}`,
        },
      ],
      attributes: {
        maxLength: 200,
        placeholder: `请输入${actualExecution}`,
      },
    },
    {
      label: penaltyMoneyText?.includes('金额') ? penaltyMoneyText : `${penaltyMoneyText}金额`, // 罚款金额
      name: 'penaltyMoney',
      initialValue: data.penaltyMoney,
      type: 'inputNumber',
      rules: [
        {
          required: true,
          message: `请输入${penaltyMoneyText?.includes('金额') ? penaltyMoneyText : `${penaltyMoneyText}金额`}`,
        },
      ],
      attributes: {
        width: '100%',
        maxLength: 200,
        min: 0,
        placeholder: `请输入${penaltyMoneyText?.includes('金额') ? penaltyMoneyText : `${penaltyMoneyText}金额`}`,
        style: { width: '100%' },
        onChange: (val) => {
          handleBindForm('penaltyMoney', val);
        },
      },
    },
    {
      label: penaltyMoney1Text?.includes('金额') ? penaltyMoney1Text : `${penaltyMoney1Text}金额`, // 罚款金额1
      name: 'penaltyMoney1',
      initialValue: data.penaltyMoney1,
      type: 'inputNumber',
      rules: [
        {
          required: penaltyMoney1Text?.includes('单边缺车') || penaltyMoney1Text?.includes('内空不足'),
          message: `请输入${penaltyMoney1Text?.includes('金额') ? penaltyMoney1Text : `${penaltyMoney1Text}金额`}`,
        },
      ],
      attributes: {
        width: '100%',
        maxLength: 200,
        ...(penaltyMoney1Text?.includes('单边缺车') || penaltyMoney1Text?.includes('内空不足')
          ? {}
          : {
            min: 0,
          }),
        placeholder: `请输入${penaltyMoney1Text?.includes('金额') ? penaltyMoney1Text : `${penaltyMoney1Text}金额`}`,
        style: { width: '100%' },
        onChange: (val) => {
          handleBindForm('penaltyMoney1', val);
        },
      },
    },
    // {
    //   label: '最终金额',
    //   name: 'finalMoney',
    //   initialValue: data.finalMoney,
    //   attributes: { disabled: true },
    // },
    {
      label: '附件',
      type: 'aneUpload',
      name: 'photos',
      initialValue: data && data.photos,
      full: true,
      attributes: {
        // disabled: true,
        fullScreen: true,
        listType: 'picture-card',
        previewtype: 'RcViewer',
        accept: 'image/*',
        nofilesTitle: true,
        defaultFileList: true,
        maxCount: 5,
        extraParams: ((data && data.photos) || []).map((item) => {
          return item;
        }),
        notOrdelete: false,
      },
    },
  ];
}

export { baseItem, DEDUCT_TYPE_ERROR_MAPPING, getErrorDescriptionOptions };
