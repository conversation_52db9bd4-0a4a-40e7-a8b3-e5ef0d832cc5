// import { Divider } from 'antd';
import { AuthWrap } from '@aned/components';
import moment from 'moment';

export const columns = (handleTableEdit) => {
  return [
    {
      title: '序号',
      dataIndex: 'number',
      fixed: 'left',
      width: 90,
      align: 'center',
      render: (text, record, index) => {
        return index + 1;
      },
    },
    {
      title: '司机工号',
      dataIndex: 'driverEmpId',
      fixed: 'left',
      align: 'center',
      ellipsis: true,
      width: 100,
    },
    {
      title: '司机姓名',
      dataIndex: 'driverName',
      fixed: 'left',
      align: 'center',
      ellipsis: true,
      width: 90,
    },
    {
      title: '司机身份证号',
      dataIndex: 'identityCard',
      align: 'center',
      ellipsis: true,
      width: 180,
    },
    {
      title: '手机号',
      dataIndex: 'driverPhone',
      align: 'center',
      ellipsis: true,
      width: 150,
    },
    {
      title: '入职日期',
      dataIndex: 'enrollmentDate',
      align: 'center',
      ellipsis: true,
      width: 150,
    },
    {
      title: '离职日期',
      dataIndex: 'resignationDate',
      align: 'center',
      ellipsis: true,
      width: 150,
    },
    {
      title: '年份',
      dataIndex: 'year',
      align: 'center',
      ellipsis: true,
      width: 90,
      render: (tag) => tag && `${tag}年`,
    },
    {
      title: '月份',
      dataIndex: 'month',
      align: 'center',
      ellipsis: true,
      width: 90,
      render: (tag) => tag && `${tag}月`,
    },
    {
      title: '所属车队',
      dataIndex: 'fleetName',
      align: 'center',
      ellipsis: true,
      width: 100,
    },
    {
      title: '公里数',
      dataIndex: 'kilometres',
      align: 'center',
      ellipsis: true,
      width: 100,
    },
    {
      title: '车队对应驾驶员有效考勤天数',
      dataIndex: 'fleetAttendanceDays',
      ellipsis: true,
      align: 'center',
      width: 200,
    },
    {
      title: '驾驶员总考勤天数',
      dataIndex: 'totalAttendanceDays',
      ellipsis: true,
      align: 'center',
      width: 180,
    },
    {
      title: '车队考勤占驾驶员总考勤比例',
      dataIndex: 'fleetAttendanceRate',
      ellipsis: true,
      align: 'center',
      width: 200,
    },
    {
      title: '基本工资',
      dataIndex: 'baseSalary',
      ellipsis: true,
      align: 'center',
      width: 90,
    },
    {
      title: '加班工资',
      dataIndex: 'overtimeSalary',
      ellipsis: true,
      align: 'center',
      width: 90,
    },
    {
      title: '全勤奖',
      dataIndex: 'fullAttendanceReward',
      ellipsis: true,
      align: 'center',
      width: 90,
    },
    {
      title: '病假工资',
      dataIndex: 'sickLeaveSalary',
      ellipsis: true,
      align: 'center',
      width: 90,
    },
    {
      title: '基础工资小计',
      dataIndex: 'basicSalarySubtotal',
      ellipsis: true,
      align: 'center',
      width: 130,
    },
    {
      title: '安全奖',
      dataIndex: 'safeReward',
      ellipsis: true,
      align: 'center',
      width: 90,
    },
    {
      title: '安全奖罚',
      dataIndex: 'safetyRewardPunishment',
      ellipsis: true,
      align: 'center',
      width: 90,
    },
    {
      title: '公里提成',
      dataIndex: 'milesSubsidy',
      ellipsis: true,
      align: 'center',
      width: 90,
    },
    {
      title: '甩挂固定工资',
      dataIndex: 'hangCircuitSubsidy',
      ellipsis: true,
      align: 'center',
      width: 130,
    },
    {
      title: '固定工资',
      dataIndex: 'fixedSalary',
      ellipsis: true,
      align: 'center',
      width: 130,
    },
    {
      title: '工龄奖',
      dataIndex: 'workYearsReward',
      ellipsis: true,
      align: 'center',
      width: 90,
    },
    {
      title: '内部介绍奖',
      dataIndex: 'introduceReward',
      ellipsis: true,
      align: 'center',
      width: 90,
    },
    {
      title: '节油奖',
      dataIndex: 'saveOilReward',
      ellipsis: true,
      align: 'center',
      width: 90,
    },
    {
      title: '资产奖惩',
      dataIndex: 'punishments',
      align: 'center',
      ellipsis: true,
      width: 90,
    },
    {
      title: '奖励小计',
      dataIndex: 'rewardSubtotal',
      align: 'center',
      ellipsis: true,
      width: 90,
    },
    {
      title: '出勤补贴',
      dataIndex: 'daysSubsidy',
      ellipsis: true,
      align: 'center',
      width: 90,
    },
    {
      title: '主驾补贴',
      dataIndex: 'mainDriverSubsidy',
      ellipsis: true,
      align: 'center',
      width: 90,
    },
    {
      title: '工伤补贴',
      dataIndex: 'injurySubsidy',
      ellipsis: true,
      align: 'center',
      width: 90,
    },
    {
      title: '组长补贴',
      dataIndex: 'leaderSubsidy',
      ellipsis: true,
      align: 'center',
      width: 90,
    },
    {
      title: '机动车补贴',
      dataIndex: 'driverMileSubsidy',
      ellipsis: true,
      align: 'center',
      width: 90,
    },
    {
      title: '甩挂计提',
      dataIndex: 'hangCircuitAccrual',
      ellipsis: true,
      align: 'center',
      width: 90,
    },
    {
      title: '不足补足补贴',
      dataIndex: 'supplyApply',
      ellipsis: true,
      align: 'center',
      width: 120,
    },
    {
      title: '宿舍补贴',
      dataIndex: 'dormSubsidy',
      ellipsis: true,
      align: 'center',
      width: 90,
    },
    {
      title: '扣分补贴',
      dataIndex: 'pointReduceSubsidy',
      ellipsis: true,
      align: 'center',
      width: 90,
    },
    {
      title: '工资校正',
      dataIndex: 'salaryAdjust',
      ellipsis: true,
      align: 'center',
      width: 90,
    },
    {
      title: '特殊申请',
      dataIndex: 'specialApply',
      ellipsis: true,
      align: 'center',
      width: 90,
    },
    {
      title: '网点补贴',
      dataIndex: 'networkSubsidy',
      align: 'center',
      ellipsis: true,
      width: 90,
    },
    {
      title: '子女教育基金',
      dataIndex: 'childrenEducationFund',
      align: 'center',
      ellipsis: true,
      width: 130,
    },
    {
      title: '补贴小计',
      dataIndex: 'subsidySubtotal',
      align: 'center',
      ellipsis: true,
      width: 90,
    },
    {
      title: '违章扣款',
      dataIndex: 'violationFine',
      align: 'center',
      width: 90,
      ellipsis: true,
    },
    {
      title: '时效扣款',
      dataIndex: 'agingFine',
      align: 'center',
      width: 90,
      ellipsis: true,
    },
    {
      title: '行为扣款',
      dataIndex: 'behaviorFine',
      align: 'center',
      width: 90,
      ellipsis: true,
    },
    {
      title: '维保扣款',
      dataIndex: 'maintainDeduction',
      align: 'center',
      width: 90,
      ellipsis: true,
    },
    {
      title: '行政扣款',
      dataIndex: 'waterElecDeduction',
      align: 'center',
      width: 90,
      ellipsis: true,
    },
    {
      title: 'APP打卡扣款',
      dataIndex: 'appCardInDeduction',
      align: 'center',
      width: 120,
      ellipsis: true,
    },
    {
      title: 'adas扣款',
      dataIndex: 'adasDeduction',
      align: 'center',
      width: 90,
      ellipsis: true,
    },
    {
      title: '考勤打卡扣款',
      dataIndex: 'clockInFine',
      align: 'center',
      width: 120,
      ellipsis: true,
    },
    {
      title: '物损扣款',
      dataIndex: 'materialDamage',
      align: 'center',
      width: 90,
      ellipsis: true,
    },
    {
      title: '工衣扣款',
      dataIndex: 'workClothes',
      align: 'center',
      width: 90,
      ellipsis: true,
    },
    {
      title: '旷工扣款',
      dataIndex: 'absenteeismFine',
      align: 'center',
      width: 90,
      ellipsis: true,
    },
    {
      title: '其他扣款',
      dataIndex: 'otherDeduction',
      ellipsis: true,
      align: 'center',
      width: 90,
    },
    {
      title: '扣款小计',
      dataIndex: 'deductionSubtotal',
      ellipsis: true,
      align: 'center',
      width: 90,
    },
    {
      title: '应发',
      dataIndex: 'planSalary',
      align: 'center',
      width: 90,
      ellipsis: true,
    },
    {
      title: '社保扣款',
      dataIndex: 'socialSecurityFine',
      align: 'center',
      width: 90,
      ellipsis: true,
    },
    {
      title: '公积金扣款',
      dataIndex: 'accumulationFundFine',
      align: 'center',
      width: 90,
      ellipsis: true,
    },
    {
      title: '本月预扣预缴税额（个税）',
      dataIndex: 'personalIncomeTax',
      align: 'center',
      width: 190,
      ellipsis: true,
    },
    {
      title: '预支工资',
      dataIndex: 'anticipateWages',
      align: 'center',
      width: 90,
      ellipsis: true,
    },
    {
      title: '个税调整',
      dataIndex: 'personalIncomeTaxReset',
      align: 'center',
      width: 90,
      ellipsis: true,
    },
    {
      title: '税后实发',
      dataIndex: 'actSalary',
      align: 'center',
      width: 90,
      ellipsis: true,
    },
    {
      title: '服务费',
      dataIndex: 'serviceCost',
      align: 'center',
      width: 90,
      ellipsis: true,
    },
    {
      title: '业务加成',
      dataIndex: 'businessAddition',
      align: 'center',
      width: 90,
      ellipsis: true,
    },
    {
      title: '费用合计',
      dataIndex: 'costTotal',
      align: 'center',
      width: 90,
      ellipsis: true,
    },
    {
      title: '开票金额',
      dataIndex: 'billingCost',
      align: 'center',
      width: 90,
      ellipsis: true,
    },
    {
      title: '银行卡开户行',
      dataIndex: 'bankName.value',
      align: 'center',
      width: 120,
      ellipsis: true,
    },
    {
      title: '银行卡号',
      dataIndex: 'bankAccount',
      align: 'center',
      width: 90,
      ellipsis: true,
    },
    {
      title: '银行卡办理人',
      dataIndex: 'bankerName',
      align: 'center',
      width: 120,
      ellipsis: true,
    },
    {
      title: '发放状态',
      dataIndex: 'payStatus.value',
      align: 'center',
      width: 90,
      ellipsis: true,
    },
    {
      title: '发放时间',
      dataIndex: 'payTime',
      align: 'center',
      width: 120,
      ellipsis: true,
      render: (tag) => tag && moment(tag).format('YYYY-MM-DD'),
    },
    {
      title: '发放失败原因',
      dataIndex: 'payFailedReason',
      align: 'center',
      width: 150,
      ellipsis: true,
    },
    {
      title: '重新提供银行卡开户行',
      dataIndex: 'reBankName.value',
      align: 'center',
      width: 180,
      ellipsis: true,
    },
    {
      title: '重新提供银行卡号',
      dataIndex: 'reBankAccount',
      align: 'center',
      width: 180,
      ellipsis: true,
    },
    {
      title: '重新提供银行卡办理人',
      dataIndex: 'reBankerName',
      align: 'center',
      width: 180,
      ellipsis: true,
    },
    {
      title: '合同甲方',
      dataIndex: 'contractPartyA',
      align: 'center',
      width: 150,
      ellipsis: true,
    },
    {
      title: '开放查询日期',
      dataIndex: 'openQueryDate',
      align: 'center',
      width: 150,
      ellipsis: true,
    },
    {
      title: '查看时间',
      dataIndex: 'viewTime',
      align: 'center',
      width: 150,
      ellipsis: true,
    },
    // {
    //   title: '饭补',
    //   dataIndex: 'dietSubsidy',
    //   ellipsis: true,
    //   align: 'center',
    //   width: 90,
    // },
    // {
    //   title: '电话补贴',
    //   dataIndex: 'phoneSubsidy',
    //   ellipsis: true,
    //   align: 'center',
    //   width: 90,
    // },
    // {
    //   title: '留存奖励',
    //   dataIndex: 'keepReward',
    //   ellipsis: true,
    //   align: 'center',
    //   width: 90,
    // },
    // {
    //   title: '考试奖励',
    //   dataIndex: 'examReward',
    //   ellipsis: true,
    //   align: 'center',
    //   width: 90,
    // },
    // {
    //   title: '加班补贴',
    //   dataIndex: 'overtimeSubsidy',
    //   ellipsis: true,
    //   align: 'center',
    //   width: 90,
    // },
    // {
    //   title: '备班补贴',
    //   dataIndex: 'backupSubsidy',
    //   ellipsis: true,
    //   align: 'center',
    //   width: 90,
    // },
    // {
    //   title: '经停补贴',
    //   dataIndex: 'stopOverSubsidy',
    //   ellipsis: true,
    //   align: 'center',
    //   width: 90,
    // },
    // {
    //   title: '单边补贴',
    //   dataIndex: 'oneWaySubsidy',
    //   align: 'center',
    //   ellipsis: true,
    //   width: 90,
    // },
    // {
    //   title: '活动奖',
    //   dataIndex: 'activityReward',
    //   ellipsis: true,
    //   align: 'center',
    //   width: 90,
    // },
    // {
    //   title: '积分年终奖',
    //   dataIndex: 'pointsYearBonus',
    //   align: 'center',
    //   width: 90,
    //   ellipsis: true,
    // },
    // {
    //   title: '物损扣款',
    //   dataIndex: 'damageDeduction',
    //   align: 'center',
    //   width: 90,
    //   ellipsis: true,
    // },
    // {
    //   title: '维保扣款（发票）',
    //   dataIndex: 'maintainDeductBill',
    //   align: 'center',
    //   width: 150,
    //   ellipsis: true,
    // },
    // {
    //   title: '工衣扣款',
    //   dataIndex: 'workClothesFine',
    //   align: 'center',
    //   width: 90,
    //   ellipsis: true,
    // },
    // {
    //   title: '扣款合计',
    //   dataIndex: 'deductionSum',
    //   align: 'center',
    //   width: 90,
    //   ellipsis: true,
    // },
    // {
    //   title: '社保调整',
    //   dataIndex: 'socialSecurityReset',
    //   align: 'center',
    //   width: 90,
    //   ellipsis: true,
    // },
    {
      title: '操作',
      width: 90,
      fixed: 'right',
      align: 'center',
      render: (record) => (
        <div>
          <AuthWrap path="/driverMoneyLookNew/edit" flag="0">
            <span onClick={() => handleTableEdit(record, 'edit')} className="ane-Table-Tooltip-span">
              编辑
            </span>
          </AuthWrap>
          {/* <Divider type="vertical" /> */}
          {/* <AuthWrap path="/driverMoneyLookNew/selectById" flag="0">
            <span
              onClick={() => handleTableEdit(record, 'view')}
              className="ane-Table-Tooltip-span"
            >
              查看
            </span>
          </AuthWrap> */}
        </div>
      ),
    },
  ];
};
