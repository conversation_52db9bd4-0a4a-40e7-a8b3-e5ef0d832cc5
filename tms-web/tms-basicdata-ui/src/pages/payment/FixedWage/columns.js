export const columns = [
  {
    title: '序号',
    dataIndex: 'number',
    width: 80,
    align: 'center',
    render: (text, record, index) => {
      return index + 1;
    },
  },
  {
    title: '所属年月',
    dataIndex: 'salaryDate',
    align: 'center',
    ellipsis: true,
    width: 100,
  },
  {
    title: '开始时间',
    dataIndex: 'startDate',
    align: 'center',
    ellipsis: true,
    width: 170,
  },
  {
    title: '结束时间',
    dataIndex: 'endDate',
    align: 'center',
    ellipsis: true,
    width: 170,
  },
  {
    title: '司机姓名',
    dataIndex: 'empName',
    align: 'center',
    ellipsis: true,
    width: 120,
  },
  {
    title: '司机工号',
    dataIndex: 'empId',
    align: 'center',
    ellipsis: true,
    width: 120,
  },
  {
    title: '所属车队',
    dataIndex: 'fleetName',
    align: 'center',
    ellipsis: true,
    width: 120,
  },
  {
    title: '工资标准',
    dataIndex: 'salary',
    align: 'center',
    ellipsis: true,
    width: 100,
  },
  {
    title: '创建时间',
    dataIndex: 'createTime',
    align: 'center',
    ellipsis: true,
    width: 200,
  },
  {
    title: '特殊员工',
    dataIndex: 'isSpecialEmployee',
    align: 'center',
    ellipsis: true,
    width: 200,
    render: (isSpecialEmployee) => isSpecialEmployee?.value
  },
  {
    title: '创建人',
    dataIndex: 'createName',
    align: 'center',
    ellipsis: true,
    width: 100,
  }
];
