import { Divider, Tooltip } from 'antd';
import moment from 'moment';
import { AuthWrap } from '@aned/components';
import { renderOptions } from '@/utils/utils';
import { arrayToOptions } from '@/utils/select';

const search_form = (attr) => [
  {
    label: '批次查询',
    type: 'Select',
    key: 'batchNumber',
    option: attr?.selectBatchNumberList?.map((itm) => ({ value: itm, label: itm })),
    onFocus: attr?.handleFormSearch.bind(this, 'batchNumber'),
    onSearch: attr?.handleFormSearch.bind(this, 'batchNumber'),
    allowClear: true,
    showSearch: true,
  },

  {
    type: 'Select',
    key: 'carRelyCompany',
    label: '所属公司',
    option:  attr?.serProData && attr?.serProData.map(item => ({ value: item.carrierName, label: item.carrierName })),
    onFocus: attr?.handleFormSearch.bind(this, 'carRelyCompany'),
    onSearch: attr?.handleFormSearch.bind(this, 'carRelyCompany'),
    allowClear: true,
    showSearch: true,
  },
  {
    label: '车牌号',
    type: 'Select',
    key: 'carTrailerId',
    option: attr?.carTrailerOption,
    onFocus: attr?.handleFormSearch.bind(this, 'carTrailer'),
    onSearch: attr?.handleFormSearch.bind(this, 'carTrailer'),
    allowClear: true,
    showSearch: true,
  },
  {
    label: '状态',
    type: 'Select',
    key: 'checkStatus',
    option: renderOptions(attr?.CategoryData, 91000),
    allowClear: true,
  },
  {
    label: '提交人',
    type: 'Select',
    key: 'createName',
    option: attr?.createLikeByName?.map((itm) => ({ value: itm, label: itm })),
    onFocus: attr?.handleFormSearch.bind(this, 'createName'),
    onSearch: attr?.handleFormSearch.bind(this, 'createName'),
    allowClear: true,
    showSearch: true,
  },
  {
    label: '提交时间',
    type: 'DatePicker',
    firstDateKey: 'startTime',
    lastDateKey: 'endTime',
    dateFormat: 'YYYY-MM-DD HH:mm:ss',
    allowClear: false,
    mesdate: 1,
    defaultValue: {
      startTime: moment().add(-1, 'month').format('YYYY-MM-DD 00:00:00'),
      endTime: moment().format('YYYY-MM-DD HH:mm:ss'),
    },
  },
  {
    label: '巡检车队',
    type: 'Select',
    key: 'createFleetId',
    option: arrayToOptions(attr?.effChoiceList, 'fleetName', 'id'),
    onFocus: attr?.handleFormSearch.bind(this, 'fleetName'),
    onSearch: attr?.handleFormSearch.bind(this, 'fleetName'),
    allowClear: true,
    showSearch: true,
  },
  {
    label: '车辆所属车队（被检查主体所属车队）',
    key: 'reviewedFleetId',
    type: 'Select',
    option: arrayToOptions(attr?.effChoiceList, 'fleetName', 'id'),
    onFocus: attr?.handleFormSearch.bind(this, 'fleetName'),
    onSearch: attr?.handleFormSearch.bind(this, 'fleetName'),
    allowClear: true,
    showSearch: true,
  },
  {
    label: '车辆是否停用',
    type: 'Select',
    key: 'fleetStatus',
    option: renderOptions(attr?.CategoryData, 12300),
    allowClear: true,
  },
];

export const columns = (attr) => [
  {
    title: '检查批次',
    dataIndex: 'batchNumber',
    formProps: search_form(attr),
    width: 150,

  },
  {
    title: '车牌号',
    dataIndex: 'carTrailerId',
    // formProps: search_form(attr),
    width: 150,
  },
  {
    title: "所属公司",
    dataIndex: 'carRelyCompany',
    width: 150,
  },
  {
    title: '状态',
    dataIndex: 'checkStatus.value',
    width: 150,
  },
  {
    title: '提交人',
    dataIndex: 'createName',
    width: 150,
  },
  {
    title: '提交时间',
    dataIndex: 'createTime',
    width: 160,
  },
  {
    title: '同行人',
    dataIndex: 'peersWhoPojoList',
    width: 150,
    render: (record) => {
      const peersWhoName = record?.map((element) => element.peersWhoName).join(',');
      return (
        <Tooltip placement="topLeft" title={peersWhoName}>
          {peersWhoName}
        </Tooltip>
      );
    },
  },
  {
    title: '处理人',
    dataIndex: 'handleName',
    width: 150,
    render: (_, record) => record?.handleUserPojo?.handleName,
  },
  {
    title: '处理时间',
    dataIndex: 'handleTime',
    width: 160,
    render: (_, record) => record?.handleUserPojo?.handleTime,
  },
  {
    title: '巡检的车队',
    dataIndex: 'createFleetName',
    width: 150,
  },
  {
    title: '被检查主体所属车队',
    dataIndex: 'reviewedFleetName',
    width: 150,
  },
  {
    title: '操作',
    width: 120,
    align: 'center',
    export: false,
    fixed: 'right',
    render: (record) => (
      <div>
        {record && record.checkStatus && record.checkStatus.name !== 'END' && (
          <AuthWrap path="/checkVehicle/updateHandleBtn" flag="0">
            <span onClick={() => attr.handleTableEdit(record, 'edit')} className="ane-Table-Tooltip-span">
              {record?.checkStatus?.name === 'PENDING' ? '去处理' : '去抽查'}
            </span>
            <Divider type="vertical" />
          </AuthWrap>
        )}
        <span onClick={() => attr.handleTableEdit(record, 'view')} className="ane-Table-Tooltip-span">
          详情
        </span>
      </div>
    ),
  },
];
