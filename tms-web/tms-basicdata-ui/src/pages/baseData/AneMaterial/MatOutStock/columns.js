import { AuthWrap } from '@aned/components';
import { Divider } from 'antd';
import moment from 'moment';
// 单日
const _formProps = (attr) => {
  const { loginInfo = {} } = attr;
  return [
    {
      type: 'Select',
      key: 'matOutStockId',
      label: '出库编号',
      option: attr?.stockIdList,
      onFocus: attr.handleFormSearch.bind(this, 'matOutStockId'),
      onSearch: attr.handleFormSearch.bind(this, 'matOutStockId'),
      allowClear: true,
      showSearch: true,
    },
    {
      type: 'Select',
      key: 'fleetName',
      label: '出库车队',
      // disabled: !loginInfo?.chiefFlag || false,
      option: attr?.effChoiceList.map((itm) => ({
        value: itm.fleetName,
        label: itm.fleetName,
      })),
      onFocus: attr.handleFormSearch.bind(this, 'fleetName'),
      onSearch: attr.handleFormSearch.bind(this, 'fleetName'),
      allowClear: true,
      showSearch: true,
      defaultValue: {
        fleetName: loginInfo?.fleetName,
      },
    },
    {
      type: 'Select',
      key: 'status',
      label: '状态',
      option: (attr?.CategoryData[54100] || [])?.map((itm) => ({ value: itm.name, label: itm.value })),
      allowClear: true,
      showSearch: true,
    },
    {
      type: 'Select',
      key: 'createName',
      label: '创建人',
      option: attr?.createNameList,
      onFocus: attr.handleFormSearch.bind(this, 'createName'),
      onSearch: attr.handleFormSearch.bind(this, 'createName'),
      allowClear: true,
      showSearch: true,
    },
    {
      type: 'DatePicker',
      label: '创建时间',
      firstDateKey: 'createTimeLeft',
      lastDateKey: 'createTimeRight',
      dateFormat: 'YYYY-MM-DD HH:mm:ss',
      allowClear: true,
      showTime: {
        hideDisabledOptions: true,
        defaultValue: [moment('00:00:00', 'HH:mm:ss'), moment('23:59:59', 'HH:mm:ss')],
      },
    },
    {
      type: 'Select',
      key: 'reviewName',
      label: '复核人',
      option: attr?.reviewNameList,
      onFocus: attr.handleFormSearch.bind(this, 'reviewName'),
      onSearch: attr.handleFormSearch.bind(this, 'reviewName'),
      allowClear: true,
      showSearch: true,
    },
    {
      type: 'DatePicker',
      label: '复核时间',
      firstDateKey: 'reviewTimeLeft',
      lastDateKey: 'reviewTimeRight',
      dateFormat: 'YYYY-MM-DD HH:mm:ss',
      allowClear: true,
      showTime: {
        hideDisabledOptions: true,
        defaultValue: [moment('00:00:00', 'HH:mm:ss'), moment('23:59:59', 'HH:mm:ss')],
      },
    },
    // {
    //   type: 'Select',
    //   key: 'panshiWbNum',
    //   label: '磐石维保号',
    //   option: attr?.panshiWbNumList,
    //   onFocus: attr.handleFormSearch.bind(this, 'panshiWbNum'),
    //   onSearch: attr.handleFormSearch.bind(this, 'panshiWbNum'),
    //   allowClear: true,
    //   showSearch: true,
    // },
  ];
};
export const columns = (attr) => [
  {
    title: '序号',
    dataIndex: 'number',
    width: 60,
    align: 'center',
    fixed: 'left',
    render: (text, record, index) => {
      return index + 1;
    },
    export: false,
    formProps: _formProps(attr),
  },
  {
    title: '磐石维保号',
    dataIndex: 'panshiWbNum',
    width: 120,
    ellipsis: true,
    align: 'center',
  },
  {
    title: '出库编号',
    dataIndex: 'matOutStockId',
    width: 180,
  },
  {
    title: '出库车队',
    dataIndex: 'fleetName',
    width: 180,
  },
  {
    title: '状态',
    dataIndex: 'status.value',
    width: 180,
  },
  {
    title: '物料明细',
    dataIndex: 'stockTotalCost',
    width: 80,
    render: (_, record) => {
      return (
        <div>
          {/* <AuthWrap path="/matInStock/selectMatInfoById" flag="0"> */}
          <span onClick={() => attr.handleTableEdit(record, 'selectMatInfoById')} className="ane-Table-Tooltip-span">
            查看明细
          </span>
          {/* </AuthWrap> */}
        </div>
      );
    },
  },
  {
    title: '创建人',
    dataIndex: 'createName',
    width: 180,
  },
  {
    title: '创建时间',
    dataIndex: 'createTime',
    width: 200,
  },

  {
    title: '出库复核人',
    dataIndex: 'reviewName',
    width: 200,
  },
  {
    title: '出库复核时间',
    dataIndex: 'reviewTime',
    width: 200,
  },
  {
    title: '操作',
    dataIndex: 'forAttendanceNum',
    width: 200,
    export: false,
    fixed: 'right',
    align: 'center',
    render: (_, record) => {
      //待复核的状态54102  是可以编辑 可以废弃的
      let isEdit = false;
      isEdit = record?.status.key === 54102 ? true : false;
      //其他状态
      return (
        <div>
          {isEdit && (
            <AuthWrap path="/matInStock/checkAgain" flag="0">
              <span onClick={() => attr.handleTableEdit(record, 'checkAgain')} className="ane-Table-Tooltip-span">
                复核
              </span>
              <Divider type="vertical" />
            </AuthWrap>
          )}
          {isEdit && (
            <AuthWrap path="/matInStock/scrap" flag="0">
              <span onClick={() => attr.handleTableEdit(record, 'scrap')} className="ane-Table-Tooltip-span">
                废弃
              </span>
              <Divider type="vertical" />
            </AuthWrap>
          )}
          {isEdit && (
            <AuthWrap path="/matInStock/edit" flag="0">
              <span onClick={() => attr.handleTableEdit(record, 'edit')} className="ane-Table-Tooltip-span">
                编辑
              </span>
              <Divider type="vertical" />
            </AuthWrap>
          )}
          <AuthWrap path="/matInStock/view" flag="0">
            <span onClick={() => attr.handleTableEdit(record, 'view')} className="ane-Table-Tooltip-span">
              查看
            </span>
          </AuthWrap>
        </div>
      );
    },
  },
];

//出库记录
export const inStockColumns = (handleTableEdit) => [
  {
    title: '序号',
    dataIndex: 'number',
    width: 90,
    // fixed: 'left',
    align: 'center',
    render: (text, record, index) => {
      return index + 1;
    },
  },
  {
    title: '车牌号',
    dataIndex: 'carId',
    width: 80,
    ellipsis: true,
    align: 'center',
  },
  {
    title: '车挂号',
    dataIndex: 'trailerId',
    width: 80,
    ellipsis: true,
    align: 'center',
  },
  {
    title: '物料编号',
    dataIndex: 'matId',
    width: 80,
    ellipsis: true,
    align: 'center',
  },
  {
    title: '物料名称',
    dataIndex: 'matName',
    width: 80,
    ellipsis: true,
    align: 'center',
  },

  {
    title: '规格',
    dataIndex: 'matSpecsName',
    width: 80,
    ellipsis: true,
    align: 'center',
  },
  {
    title: '单位',
    dataIndex: 'unit',
    width: 80,
    ellipsis: true,
    align: 'center',
  },

  {
    title: '数量',
    dataIndex: 'outStockNum',
    width: 80,
    ellipsis: true,
    align: 'center',
  },

  {
    title: '加权单价',
    dataIndex: 'priceAvg',
    width: 80,
    ellipsis: true,
    align: 'center',
  },

  {
    title: '金额',
    dataIndex: 'costAvg',
    width: 80,
    ellipsis: true,
    align: 'center',
  },
  {
    title: '磐石维保号',
    dataIndex: 'panshiWbNum',
    width: 120,
    ellipsis: true,
    align: 'center',
  },
  {
    title: '备注',
    dataIndex: 'remark',
    width: 80,
    ellipsis: true,
    align: 'center',
  },
  {
    title: '操作',
    width: 100,
    ellipsis: true,
    align: 'center',
    // fixed: 'right',
    render: (_, record, idx) => {
      return (
        <div>
          <span onClick={() => handleTableEdit(record, 'edit', idx)} className="ane-Table-Tooltip-span">
            编辑
          </span>
          <Divider type="vertical" />
          <span onClick={() => handleTableEdit(record, 'delete', idx)} className="ane-Table-Tooltip-span">
            删除
          </span>
        </div>
      );
    },
  },
];

export const exportColumns = [
  {
    title: '出库编号',
    dataIndex: 'matOutStockId',
    width: 180,
  },
  {
    title: '出库车队',
    dataIndex: 'fleetName',
    width: 80,
  },
  {
    title: '状态',
    dataIndex: 'status.value',
    width: 80,
  },
  {
    title: '车牌号',
    dataIndex: 'carId',
    width: 80,
    ellipsis: true,
    align: 'center',
  },
  {
    title: '车挂号',
    dataIndex: 'trailerId',
    width: 80,
    ellipsis: true,
    align: 'center',
  },
  {
    title: '物料编号',
    dataIndex: 'matId',
    width: 80,
    ellipsis: true,
    align: 'center',
  },
  {
    title: '物料名称',
    dataIndex: 'matName',
    width: 80,
    ellipsis: true,
    align: 'center',
  },

  {
    title: '规格',
    dataIndex: 'matSpecsName',
    width: 80,
    ellipsis: true,
    align: 'center',
  },
  {
    title: '单位',
    dataIndex: 'unit',
    width: 80,
    ellipsis: true,
    align: 'center',
  },

  {
    title: '数量',
    dataIndex: 'outStockNum',
    width: 80,
    ellipsis: true,
    align: 'center',
  },
  {
    title: '磐石维保号',
    dataIndex: 'panshiWbNum',
    width: 80,
    ellipsis: true,
    align: 'center',
  },
  {
    title: '备注',
    dataIndex: 'remark',
    width: 80,
    ellipsis: true,
    align: 'center',
  },
  {
    title: '创建人',
    dataIndex: 'createName',
    width: 80,
  },
  {
    title: '创建时间',
    dataIndex: 'createTime',
    width: 180,
  },
  {
    title: '出库复核人',
    dataIndex: 'reviewName',
    width: 80,
  },
  {
    title: '出库复核时间',
    dataIndex: 'reviewTime',
    width: 180,
  },
  {
    title: '加权单价',
    dataIndex: 'priceAvg',
    width: 180,
  },
  {
    title: '金额',
    dataIndex: 'costAvg',
    width: 180,
  },
];
