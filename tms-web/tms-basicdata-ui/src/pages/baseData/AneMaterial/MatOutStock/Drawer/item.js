export const basicItem = (data, attr) => [
  {
    label: '出库编号',
    name: 'matOutStockId',
    initialValue: data?.matOutStockId,
    attributes: { disabled: true },
  },
  {
    type: 'select',
    label: '出库车队',
    name: 'fleetName',
    initialValue: data?.fleetName && data?.fleetId ? { label: data.fleetName, key: data?.fleetId } : undefined,
    options: attr?.effChoiceList.map((itm) => ({
      value: itm.id,
      label: itm.fleetName,
    })),
    rules: [{ required: true, message: '请选择出库车队' }],
    attributes: {
      onFocus: () => attr.handleSearch('fleetName'),
      onSearch: (val) => attr.handleSearch('fleetName', val),
      allowClear: true,
      showSearch: true,
      // disabled: !attr.loginInfo?.chiefFlag || false,
      disabled: false,
      labelInValue: true,
      optionFilterProp: 'children',
    },
  },
];

export const addMatItem = (data, attr) => [
  {
    label: '车牌号',
    name: 'carId',
    type: 'select',
    initialValue: data?.carId,
    options: attr.selectCarList,
    attributes: {
      disabled: attr?.isType === 'edit',
      showSearch: true,
      allowClear: true,
      onFocus: () => attr.handleSelectChange('carId'),
      onSearch: (val) => attr.handleSelectChange('carId', val),
    },
  },
  {
    label: '车挂号',
    name: 'trailerId',
    type: 'select',
    initialValue: data?.trailerId,
    options: attr.allTrailerId.map((item) => ({
      value: item.id,
      label: item.id,
    })),
    attributes: {
      disabled: attr?.isType === 'edit',
      showSearch: true,
      allowClear: true,
      onFocus: () => attr.handleSelectChange('trailerId'),
      onSearch: (val) => attr.handleSelectChange('trailerId', val),
    },
  },
  {
    label: '物料编号',
    name: 'matId',
    initialValue: data?.matId,
    attributes: { disabled: true },
    rules: [{ required: true, message: '请输入' }],
  },
  {
    label: '物料名称',
    name: 'matName',
    type: 'select',
    options: attr?.matNameList,
    initialValue: data?.matName || undefined,
    attributes: {
      showSearch: true,
      allowClear: true,
      onFocus: () => attr.handleSelectChange('matName'),
      onSearch: (val) => attr.handleSelectChange('matName', val),
      onChange: attr.handleSelected.bind(this, 'matName'),
    },
    rules: [{ required: true, message: '请输入' }],
  },
  {
    label: '规格',
    name: 'matSpecsName',
    // type: 'select',
    initialValue: data?.matSpecsName,
    options: attr?.matSpecsNameList,
    attributes: {
      showSearch: true,
      allowClear: true,
      disabled: true
    },
    rules: [{ required: true, message: '请输入' }],
  },

  {
    label: '单位',
    name: 'unit',
    // type: 'select',
    options: attr?.unitList,
    initialValue: data?.unit,
    attributes: {
      showSearch: true,
      allowClear: true,
      disabled: true
    },
    rules: [{ required: true, message: '请输入' }],
  },

  {
    type: 'inputNumber',
    label: '数量',
    name: 'outStockNum',
    initialValue: data?.outStockNum,
    attributes: {
      width: '100%',
      min: 0,
      precision: 2,
    },
    rules: [{ required: true, message: '请输入' }],
  },
  {
    type: 'inputNumber',
    label: '磐石维保号',
    name: 'panshiWbNum',
    initialValue: data?.panshiWbNum,
    attributes: {
      width: '100%',
      min: 0,
      precision: 2,
    },
    rules: [{ required: true, message: '请输入' }],
  },

  {
    label: '备注',
    name: 'remark',
    initialValue: data?.remark,
    attributes: { allowClear: true },
    rules: [{ required: false, message: '请输入' }],
  }
];

// 编辑人信息
export const editDetailItem = (data) => [
  {
    label: '创建人',
    name: 'createName',
    initialValue: data?.createName,
  },
  {
    label: '创建时间',
    name: 'createTime',
    initialValue: data?.createTime,
  },
  {
    label: '车队复核人',
    name: 'reviewName',
    initialValue: data?.reviewName,
  },
  {
    label: '车队复核时间',
    name: 'reviewTime',
    initialValue: data?.reviewTime,
  },
];
