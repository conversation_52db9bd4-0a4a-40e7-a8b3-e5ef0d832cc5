import React, { useRef } from 'react';
import DrawerFooter from '@/components/DrawerFooter';
import { basicItem, editDetailItem } from './item';
import { inStockColumns } from '../columns';
import { Drawer, Button, Icon, Modal, message } from 'antd';
import FormContent from '@/components/FormContent';
import { KCard } from '@aned/bizcomponent';
import { asyncAwaitForm } from '@/utils/from';
import MatDetailTable from './matDetailTable';
import TableResizable from '@aned/table-resizable';
import { connect } from 'umi';

const drawerDetail = (props) => {
  const baseRef = useRef();
  const {
    isShow,
    isType,
    baseDetail,
    matInfo,
    dispatch,
    isModalType,
    updateData,
    addNewInfo,
    editingKey,
    loginInfo,
    tableRef,
    CommonInterface: { effChoiceList },
  } = props;

  // 关闭弹窗
  const onClose = () => {
    dispatch({
      type: 'MatOutStock/rest',
    });
  };

  const handleSearch = (type, value = '') => {
    if (type === 'fleetName') {
      dispatch({
        type: 'CommonInterface/selectEffChoiceList',
        payload: {
          fleetName: value,
        },
      });
    }
  };

  const handleOperate = (type) => {
    // 删除物料明细
    if (type === 'deleteMat') {
      //新增，删除，不走数据库，直接删除
      if (isType === 'add' && isModalType === 'delModal') {
        const _matInfo = matInfo.concat();
        _matInfo.splice(editingKey, 1);
        updateData({
          matInfo: _matInfo,
          isModalType: '',
        });
      } else {
        dispatch({
          type: 'MatOutStock/deleteOneDetail',
          payload: {
            delParm: {
              matOutStockDetailId: addNewInfo?.matOutStockDetailId,
              matOutStockId: addNewInfo?.matOutStockId,
            },
            matOutStockId: baseDetail?.matOutStockId,
            callback: () => tableRef?.current?.onSearch(),
          },
        });
      }
    }
  };

  // 物料明细操作
  const handleTableEdit = (record, type, idx) => {
    updateData({
      editingKey: idx,
      addNewInfo: record,
      isModalType: type === 'edit' ? 'colEditModal' : 'delModal', //表格单条数据编辑
    });
  };

  // 新增保存
  const onSave = async () => {
    let basicInfo = await asyncAwaitForm(baseRef.current);
    matInfo?.forEach((item) => {
      if (item.carId === undefined && item.trailerId === undefined) {
        return message.warn('新增物料请选择车牌号或车挂号');
      }
    });
    let pram = {
      ...basicInfo,
      matOutStockDetailPojoList: matInfo,
      fleetName: basicInfo?.fleetName?.label,
      fleetId: basicInfo?.fleetName?.key,
    };
    dispatch({
      type: 'MatOutStock/insert',
      payload: {
        ...pram,
        callback: () => tableRef?.current?.onSearch(),
      },
    });
  };

  // 复核
  const onOk = () => {
    Modal.confirm({
      content: `确定复核?`,
      cancelText: '取消',
      okText: '确定',
      onOk: () => {
        dispatch({
          type: 'MatOutStock/updateOutStockStatus',
          payload: {
            matOutStockId: baseDetail?.matOutStockId,
            newStatus: 'ALREADY_OUT_STOCK',
            oldStatus: baseDetail?.status?.name,
            callback: () => tableRef?.current?.onSearch(),
          },
        });
      },
    });
  };

  return (
    <Drawer
      title={
        (isType === 'selectMatInfoById' && '物料明细') ||
        (isType === 'view' && '详情') ||
        (isType === 'edit' && '编辑出库') ||
        (isType === 'add' && '新增出库') ||
        (isType === 'checkAgain' && '复核')
      }
      visible={isShow === 'draw'}
      destroyOnClose
      onClose={onClose}
      width={'70%'}
      bodyStyle={{ padding: '0 10px 45px 10px' }}
    >
      {isType !== 'selectMatInfoById' && (
        <KCard title="基本信息">
          <FormContent
            ref={baseRef}
            colspan={2}
            isView={isType === 'view' || isType === 'selectMatInfoById' || isType === 'checkAgain'}
            collapseType="cardform"
            dataSource={basicItem(baseDetail, {
              effChoiceList,
              loginInfo,
              handleSearch,
            })}
          />
        </KCard>
      )}

      <KCard title="物料明细">
        <TableResizable
          dataSource={matInfo || []}
          rowKey="matOutStockDetailId"
          columns={
            isType === 'edit' || isType === 'add'
              ? inStockColumns(handleTableEdit).filter((_, index) => ![10, 11].includes(index))
              : inStockColumns(handleTableEdit).slice(0, -1)
          }
          pagination={true}
        />
        {isType === 'add' && (
          <Button
            type="dashed"
            block
            style={{ marginTop: 10, marginBottom: 10 }}
            onClick={() =>
              updateData({
                isModalType: 'colAddModal',
                addNewInfo: {}
              })
            }
          >
            <Icon type="plus" />
            新增物料
          </Button>
        )}
      </KCard>

      <MatDetailTable updateData={updateData} baseDetail={baseDetail} tableRef={tableRef} />
      {(isType === 'edit' || isType === 'view') && (
        <KCard title="审核信息">
          <FormContent colspan={3} isView={true} collapseType="cardform" dataSource={editDetailItem(baseDetail, {})} />
        </KCard>
      )}
      <Modal
        title="物料删除"
        visible={isModalType === 'delModal'}
        onOk={() => handleOperate('deleteMat')}
        onCancel={() => {
          updateData({
            isModalType: '',
          });
        }}
        okText="确认"
        cancelText="取消"
      >
        <p>确认删除这条数据？</p>
      </Modal>
      <DrawerFooter>
        <Button onClick={onClose} style={{ marginRight: 10 }}>
          取消
        </Button>
        {isType === 'add' && (
          <Button type="primary" onClick={onSave} style={{ marginRight: 10 }}>
            保存
          </Button>
        )}
        {isType === 'checkAgain' && (
          <Button type="primary" onClick={onOk} style={{ marginRight: 10 }}>
            确定
          </Button>
        )}
      </DrawerFooter>
    </Drawer>
  );
};

export default connect((state) => ({
  ...state.MatOutStock,
  CommonInterface: state.CommonInterface,
  loading: state.loading,
}))(drawerDetail);
