import React from 'react';
import { SearchFormPro } from '@aned/bizcomponent';
import { connect } from 'umi';
import dayjs from 'dayjs';
const Search = (props) => {
  const {
    sigleTotal: { filter, activeKey },
    CommonInterface: { selectCarList, effChoiceList, CarBrandDate },
  } = props;
  // 搜索
  const onSearch = () => {
    props.dispatch({
      type: 'sigleTotal/updateData',
      payload: {
        page: 1,
        exportSearch: { ...filter },
      },
    });
    props.dispatch({
      type: activeKey === '1' ? 'sigleTotal/selectServiceStatistics' : 'sigleTotal/selectMonthFeeStatistics',
    });
  };
  // 重置
  const onReset = () => {
    props.dispatch({
      type: 'sigleTotal/updateData',
      payload: {
        filter: {
          startTime: dayjs().subtract(1, 'month').format('YYYY-MM-DD HH:mm:ss'),
          endTime: dayjs().format('YYYY-MM-DD HH:mm:ss')
        },
      },
    });
  };
  //模糊搜索
  const handleSelectChange = (type, value = '') => {
    // 车牌
    if (type === 'carId') {
      props.dispatch({
        type: 'CommonInterface/selectByCarLike',
        payload: {
          id: value,
        },
      });
    }
    if (type === 'fleetName') {
      props.dispatch({
        type: 'CommonInterface/selectEffChoiceList',
        payload: {
          fleetName: value,
        },
      });
    }
  };
  return (
    <SearchFormPro
      openup={false}
      filter={filter}
      onChange={(data) => {
        props.dispatch({
          type: 'sigleTotal/updateData',
          payload: {
            filter: { ...filter, ...data },
          },
        });
      }}
      button={[
        {
          type: 'primary',
          label: '查询',
          onClick: onSearch,
        },
        {
          label: '重置',
          onClick: onReset,
        },
      ]}
      dataSource={[
        {
          type: 'DatePicker',
          label: '报修日期',
          firstDateKey: 'startTime',
          lastDateKey: 'endTime',
          mesdate: 1,
          dateFormat: 'YYYY-MM-DD HH:mm:ss',
          allowClear: false,
        },
        {
          type: 'Select',
          key: 'carId',
          label: '车牌号',
          option: selectCarList,
          onSearch: handleSelectChange.bind(this, 'carId'),
          onSelect: handleSelectChange.bind(this, 'carId'),
          onFocus: handleSelectChange.bind(this, 'carId'),
          allowClear: true,
          showSearch: true,
        },
        {
          type: 'Select',
          key: 'fleetId',
          label: '所属车队',
          option: effChoiceList.map((itm) => ({ label: itm.fleetName, value: itm.id })),
          onFocus: handleSelectChange.bind(this, 'fleetName'),
          onSearch: handleSelectChange.bind(this, 'fleetName'),
          allowClear: true,
          showSearch: true,
        },
        {
          type: 'Select',
          key: 'carBrand',
          label: '车辆品牌',
          option:
            CarBrandDate &&
            CarBrandDate.map((itm) => ({
              id: itm.id,
              value: itm.carBrandName,
              label: itm.carBrandName,
            })),
          allowClear: true,
          showSearch: true,
        },
      ]}
    />
  );
};
export default connect((state) => ({
  sigleTotal: state.sigleTotal,
  CommonInterface: state.CommonInterface,
}))(Search);
