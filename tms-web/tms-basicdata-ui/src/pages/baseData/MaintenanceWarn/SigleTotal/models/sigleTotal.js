import {
  selectServiceStatistics,
  selectMonthFeeStatistics,
  // selectPage,
  // fetchExport
} from '@/services/baseData/MaintenanceWarn';
import dayjs from 'dayjs';
//import {message} from 'antd'
export default {
  namespace: 'sigleTotal',
  state: {
    activeKey: '1',
    total: 0,
    pageSize: 20,
    page: 1,
    // ----------------------
    total2: 0,
    pageSize2: 20,
    page2: 1,
    // table高度
    tableOffsetHeight: 0,
    // table分页数据
    dataSource: [],
    dataSource2: [],
    // 查询
    filter: {
      startTime: dayjs().subtract(1, 'month').format('YYYY-MM-DD HH:mm:ss'),
      endTime: dayjs().format('YYYY-MM-DD HH:mm:ss')
    },
    // 导出
    exportSearch: {},
    // 导出弹框
    exportvisible: false,
  },
  effects: {
    //  服务统计分页查询列表
    *selectServiceStatistics(payload, { call, put, select }) {
      const param = yield select(({ sigleTotal }) => ({
        page: sigleTotal.page,
        pageSize: sigleTotal.pageSize,
        queryData: {
          ...sigleTotal.filter,
        },
      }));
      const data = yield call(selectServiceStatistics, param);
      if (data.code === 1) {
        yield put({
          type: 'updateData',
          payload: {
            dataSource: data.data.rows || [],
            total: data.data.total,
          },
        });
      }
    },
    // 服务统计跳转页面
    *goToPage({ payload: { page, pageSize } }, { put }) {
      yield put({ type: 'updateData', payload: { ...{ page, pageSize } } });
      yield put({ type: 'selectServiceStatistics' });
    },

    // 月费用统计分页
    *selectMonthFeeStatistics(payload, { call, put, select }) {
      const param = yield select(({ sigleTotal }) => ({
        page: sigleTotal.page2,
        pageSize: sigleTotal.pageSize2,
        queryData: {
          ...sigleTotal.filter,
        },
      }));
      const data = yield call(selectMonthFeeStatistics, param);
      if (data.code === 1) {
        yield put({
          type: 'updateData',
          payload: {
            dataSource2: data.data.rows || [],
            total2: data.data.total,
          },
        });
      }
    },
    // 服务统计跳转页面
    *goToPage2({ payload: { page, pageSize } }, { put }) {
      yield put({ type: 'updateData', payload: { page2: page, pageSize2: pageSize } });
      yield put({ type: 'selectMonthFeeStatistics' });
    },
    // 导出
    // *fetchExport({ payload }, { call, put }) {
    //   const data = yield call(fetchExport, payload);
    //   if (data.code === 1) {
    //     message.success(data.message);
    //     yield put({ type: 'updateData', payload: { exportvisible: false } });
    //   }else {
    //     message.warn(data.message);
    //   }
    // },
  },
  reducers: {
    updateData(state, { payload }) {
      return { ...state, ...payload };
    },
  },
};
