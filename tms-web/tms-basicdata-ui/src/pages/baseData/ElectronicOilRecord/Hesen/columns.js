// import React from 'react';
// import { Divider } from 'antd';
import { AuthWrap } from '@aned/components';
import { Popconfirm } from 'antd';

export const columns = (handleTableEdit) => [
  {
    title: '结算状态',
    dataIndex: 'settlementState.value',
    align: 'center',
    width: 100,
    ellipsis: true,
    fixed: 'left',
  },
  {
    title: '创建时间',
    dataIndex: 'createTime',
    align: 'center',
    width: 120,
    ellipsis: true,
  },
  {
    title: '在职状态',
    dataIndex: 'jobState.value',
    align: 'center',
    width: 90,
    ellipsis: true,
  },
  {
    title: '司机姓名',
    dataIndex: 'driverName',
    align: 'center',
    width: 120,
    ellipsis: true,
  },
  {
    title: '司机工号',
    dataIndex: 'driverEmpId',
    align: 'center',
    width: 120,
    ellipsis: true,
  },
  {
    title: '手机号',
    dataIndex: 'phone',
    align: 'center',
    width: 120,
    ellipsis: true,
  },
  {
    title: '绑定车牌 ',
    dataIndex: 'carId',
    align: 'center',
    width: 120,
    ellipsis: true,
  },
  {
    title: '所属车队',
    dataIndex: 'fleetName',
    align: 'center',
    width: 120,
    ellipsis: true,
  },
  {
    title: '用气类型',
    dataIndex: 'gasType',
    align: 'center',
    width: 90,
    ellipsis: true,
  },
  {
    title: '优惠前单价(元/L)',
    dataIndex: 'originalPrice',
    width: 140,
    ellipsis: true,
    align: 'center',
  },
  {
    title: '单价(元/L)',
    dataIndex: 'gasPrice',
    width: 90,
    ellipsis: true,
    align: 'center',
  },
  {
    title: '加气量',
    dataIndex: 'gasNum',
    width: 100,
    ellipsis: true,
    align: 'center',
  },
  {
    title: '金额(元)',
    dataIndex: 'gasCost',
    width: 90,
    ellipsis: true,
    align: 'center',
  },
  {
    title: '加气时间',
    dataIndex: 'gasFillTime',
    align: 'center',
    width: 150,
    ellipsis: true,
  },
  {
    title: '气站名称',
    dataIndex: 'gasStation',
    align: 'center',
    width: 120,
    ellipsis: true,
  },
  {
    title: '绑定线路',
    dataIndex: 'shipmentScheduleName',
    align: 'center',
    width: 120,
    ellipsis: true,
  },
  {
    title: '绑定订单',
    dataIndex: 'orderId',
    align: 'center',
    width: 140,
    ellipsis: true,
  },
  {
    title: '服务商名称',
    dataIndex: 'serviceName',
    align: 'center',
    width: 140,
    ellipsis: true,
  },
  {
    title: '审核状态',
    dataIndex: 'auditStatus.value',
    align: 'center',
    width: 100,
    ellipsis: true,
  },
  {
    title: '三方订单号',
    dataIndex: 'threePartyOrderId',
    align: 'center',
    width: 140,
    ellipsis: true,
  },
  {
    title: '备注',
    dataIndex: 'remark',
    align: 'center',
    width: 140,
    ellipsis: true,
  },
  {
    title: '操作',
    width: 100,
    fixed: 'right',
    align: 'center',
    export: false,
    render: (record) => {
      return (
        <>
          {record.auditStatus?.name === 'PASS' && (
            <AuthWrap path="/hs/update" flag="0">
              <Popconfirm
                title="确定废弃本条?"
                overlayStyle={{ width: 150 }}
                onConfirm={() => handleTableEdit(record, 'audit')}
              >
                <span className="ane-Table-Tooltip-span">废弃</span>
              </Popconfirm>
            </AuthWrap>
          )}
        </>
      );
    },
  },
];

export default columns;
