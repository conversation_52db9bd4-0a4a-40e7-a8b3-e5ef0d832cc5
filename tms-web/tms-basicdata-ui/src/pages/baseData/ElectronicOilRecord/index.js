import React, { useState } from 'react';
import { Tabs } from 'antd';
import LaoLu from './LaoLu/index'
import <PERSON>sen from './Hesen/index'
import styles from './index.less';
export default () => {
  const [activeKey, setactiveKey] = useState('1');
  return (
    <Tabs
      tabBarStyle={{ backgroundColor: '#fff' }}
      className={styles.margin0}
      activeKey={activeKey}
      style={{ margin: 0 }}
      onChange={e => setactiveKey(e)}
    >
      <Tabs.TabPane style={{ margin: 0 }} tab="电子加油记录" key={'1'}>
        <LaoLu />
      </Tabs.TabPane>
      <Tabs.TabPane style={{ margin: 0 }} tab="电子加气" key={'2'}>
        <Hesen />
      </Tabs.TabPane>
    </Tabs>
  );
};
