import {
  selectPage,
  fetchExport,
  getConfig,
  updateConfig,
  addConfig,
  create,
  currentMonthCalculationApi
} from '@/services/baseData/SettlementAmount';
import moment from 'moment';
import { message } from 'antd';
export default {
  namespace: 'SettlementAmount',
  state: {
    total: 0,
    pageSize: 20,
    page: 1,
    // table高度
    tableOffsetHeight: 0,
    // table分页数据
    dataSource: [],
    // 更新
    filter: {
      /**默认上个月*/
      monthDate: moment().subtract(1, 'month').format('YYYY-MM'),
    },
    // 导出
    exportSearch: {},
    // 导出弹框
    exportvisible: false,

    /**定稿⽇期设置*/
    settingData: {
      yearAndMonth: moment().subtract(1, 'month').format('YYYY-MM'),
      dateAndTime: moment().subtract(1, 'month').format('YYYY-MM-10 23:59:59'),
    },
    settingVisible: false,
    // 默认新增  "add" | "update"
    settingIsAddOrUpdate: 'add',
    // table 索引项
    tabIndex: '0',
  },
  effects: {
    //  分页查询列表
    *selectPage(payload, { call, put, select }) {
      try {
        const { tabIndex, filter, page, pageSize } = yield select(({ SettlementAmount }) => ({
          tabIndex: SettlementAmount.tabIndex,
          filter: SettlementAmount.filter,
          page: SettlementAmount.page,
          pageSize: SettlementAmount.pageSize,
        }));
        const { monthDate, fleetId = null, carId = null, ...rest } = filter;
        const queryDataData  = [
          {
            monthDate,
            fleetId,
            carId,
          },{
            ...filter
          }
        ]
        const param = yield select(() => ({
          page,
          pageSize,
          queryData: queryDataData[tabIndex],
        }));

        if (param.queryData?.monthDate) {
          const newDate = moment(param.queryData?.monthDate).format('YYYYMM');
          param.queryData.monthDate = newDate;
        }
        delete param?.queryData?.radioDate;
        const data = yield call(selectPage, param);
        if (data.code === 1) {
          const dataList = (data.data.rows || []).map((item, index) => {
            return { __id: `${new Date().getTime()}_${index}`, ...item };
          });
          yield put({
            type: 'updateData',
            payload: {
              dataSource: dataList || [],
              total: data.data.total,
            },
          });
        }
      } catch (error) {
        // eslint-disable-next-line no-console
        console.error(error);
      }
    },
    // 跳转页面
    *goToPage({ payload: { page, pageSize } }, { put }) {
      yield put({ type: 'updateData', payload: { ...{ page, pageSize } } });
      yield put({ type: 'selectPage' });
    },
    // 导出
    *fetchExport({ payload }, { call, put }) {
      try {
        const param = { ...payload };
        if (param.queryCondition?.monthDate) {
          const newDate = moment(param.queryCondition?.monthDate).format('YYYYMM');
          param.queryCondition.monthDate = newDate;
        }
        delete param?.queryCondition?.radioDate;
        const data = yield call(fetchExport, param);
        if (data.code === 1) {
          message.success(data.message);
          yield put({ type: 'updateData', payload: { exportvisible: false } });
        } else {
          message.warn(data.message);
        }
      } catch (error) {
        // eslint-disable-next-line no-console
        console.log(error);
      }
    },
    // 查询配置
    *getConfig({ payload }, { call, put }) {
      try {
        const resultData = yield call(getConfig, payload);
        if (resultData && resultData.code === 1 && resultData.data) {
          const settingData = {
            yearAndMonth: moment().subtract(1, 'month').format('YYYY-MM'),
            dateAndTime: moment().subtract(1, 'month').format('YYYY-MM-10 23:59:59'),
            ...resultData?.data,
          };
          if (resultData.data?.yearAndMonth && resultData.data?.dateAndTime) {
            const date = resultData?.data.yearAndMonth + '-' + resultData.data?.dateAndTime;
            settingData.dateAndTime = moment(date).format('YYYY-MM-DD HH:mm:ss');
          }
          yield put({
            type: 'updateData',
            payload: {
              settingData,
              settingIsAddOrUpdate: resultData?.data?.dateAndTime ? 'update' : 'add',
            },
          });
        }
      } catch (error) {
        // eslint-disable-next-line no-console
        console.error(error);
      }
    },
    // 更新配置
    *updateConfig({ payload }, { call, put, select }) {
      try {
        const { settingIsAddOrUpdate, settingData } = yield select(({ SettlementAmount }) => ({
          settingIsAddOrUpdate: SettlementAmount.settingIsAddOrUpdate,
          settingData: SettlementAmount.settingData,
        }));
        let resultData;
        const params = { ...payload };

        if (settingIsAddOrUpdate === 'update') {
          params.id = settingData.id;
          resultData = yield call(updateConfig, params);
        } else {
          resultData = yield call(addConfig, params);
        }
        if (resultData && resultData.code === 1) {
          message.success(resultData?.message || '定稿日期设定更新成功');
          yield put({ type: 'goToPage', payload: { page: 1 } });
          yield put({ type: 'restConfig' });
        }
      } catch (error) {}
    },
    // 生成结算公里数
    *create(_, { call, put }) {
      try {
        let resultData = yield call(create);
        if (resultData && resultData.code === 1) {
          message.success(resultData?.message || 'excel文件导出成功，请稍后在报表管理-excel导出任务页面查看。');
          yield put({ type: 'goToPage', payload: { page: 1 } });
        }
      } catch (error) {}
    },
    // 本月结算公里数计算
    *currentMonthCalculation(_, { call }) {
      try {
        let resultData = yield call(currentMonthCalculationApi);
        if (resultData && resultData.code === 1) {
          message.success(resultData?.message || 'excel文件导出成功，请稍后在报表管理-excel导出任务页面查看。');
        }
      } catch (error) {}
    },
    *restConfig(_, { put }) {
      yield put({
        type: 'updateData',
        payload: {
          /**定稿⽇期设置*/
          settingData: {
            yearAndMonth: moment().subtract(1, 'month').format('YYYY-MM'),
            dateAndTime: moment().subtract(1, 'month').format('YYYY-MM-10 23:59:59'),
          },
          settingVisible: false,
          // 默认新增  "add" | "update"
          settingIsAddOrUpdate: 'add',
        },
      });
    },
    // 设置table索引
    *setTableIndex({ index }, { put, select }) {
      yield put({
        type: 'updateData',
        payload: {
          tabIndex: index,
        },
      });
    },
  },
  reducers: {
    updateData(state, { payload }) {
      return { ...state, ...payload };
    },
  },
};
