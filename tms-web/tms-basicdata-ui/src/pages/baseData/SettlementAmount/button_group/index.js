import { ButtonGroupPro, BizExport } from '@aned/bizcomponent';
import { connect } from 'umi';
import columns from "../table/columns"
import { message } from "antd"
import SeetingConfig from "./seeting"

const Main = (props) => {
  const {
    loading,
    dispatch,
    SettlementAmount
  } = props;
  const { exportvisible, dataSource, exportSearch } = SettlementAmount

  const updateData = (payload) => {
    dispatch({
      type: 'SettlementAmount/updateData',
      payload,
    });
  };

  // 导出
  const exporExl = () => {
    if (dataSource?.length > 0) {
      updateData({ exportvisible: true });
    } else {
      message.warn('暂无导出数据');
    }
  };
  /**导出*/
  const exporte = (cols) => {
    dispatch({
      type: 'SettlementAmount/fetchExport',
      payload: {
        queryCondition: {
          ...exportSearch,
        },
        pageName: '结算公里数',
        titleAndParams: cols,
      },
    });
  };

  /**定稿日期设置*/
  const onGetSeetingConfig = () => {
    dispatch({ type: "SettlementAmount/getConfig" })
    dispatch({
      type: "SettlementAmount/updateData",
      payload: { settingVisible: true }
    })
  }

  /**生成结算公里数*/
  const onCreate = () => {
    dispatch({
      type: "SettlementAmount/create",
      payload: { settingVisible: true }
    })
  }

  // 本月结算公里数计算
  const onSettlementMileageCalculation = () => {
    dispatch({ type: "SettlementAmount/currentMonthCalculation" })
  }

  return <div>
    <ButtonGroupPro
      button={[
        {
          type: 'primary',
          label: '导出',
          onClick: exporExl,
          path: '/car/mileage/agg/export',
        },
        {
          type: 'primary',
          label: '生成结算公里数',
          onClick: onCreate,
          path: '/car/mileage/agg/create',
        },
        {
          type: 'primary',
          label: '定稿日期设定',
          onClick: onGetSeetingConfig,
          path: '/car/mileage/agg/config/button',
        },
        {
          type: 'primary',
          label: '本月结算公里数计算',
          onClick: onSettlementMileageCalculation,
          loading: loading.effects['SettlementAmount/currentMonthCalculation'],
          path: '/car/mileage/agg/current/create',
        },
      ]}
    />

    <BizExport
      visible={exportvisible}
      columns={columns()}
      handleCancel={() => {
        updateData({ exportvisible: false })
      }}
      onExport={(cols) => {
        exporte(cols);
      }}
      unkey="dataIndex"
      templateKey="ane-settlement-amount"
      serviceCode="exportLocationService"
      pageName="结算公里数"
      loading={loading.effects['SettlementAmount/fetchExport']}
    />
    <SeetingConfig />
  </div>
}
export default connect((state) => ({
  SettlementAmount: state.SettlementAmount,
  loading: state.loading,
}))(Main);
;
