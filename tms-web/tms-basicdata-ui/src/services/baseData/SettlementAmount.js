import request from '@aned/request';

// 查询
export async function selectPage(params) {
  return request('api/car/mileage/agg/selectPage', {
    method: 'POST',
    body: params,
    reqType: 'json',
    module: 'reports',
  });
}

//导出
export async function fetchExport(params) {
  return request('api/car/mileage/agg/export', {
    method: 'POST',
    body: params,
    reqType: 'json',
    module: 'reports',
  });
}

// 查询配置
export async function getConfig(params) {
  return request('api/car/mileage/agg/config/get', {
    method: 'POST',
    body: params,
    reqType: 'json',
    module: 'reports',
  });
}

// 更新配置
export async function updateConfig(params) {
  return request('api/car/mileage/agg/config/update', {
    method: 'POST',
    body: params,
    reqType: 'json',
    module: 'reports',
  });
}

// 新增配置
export async function addConfig(params) {
  return request('api/car/mileage/agg/config/add', {
    method: 'POST',
    body: params,
    reqType: 'json',
    module: 'reports',
  });
}

// 生成结算公里数
export async function create(params) {
  return request('api/car/mileage/agg/create', {
    method: 'POST',
    body: params,
    reqType: 'json',
    module: 'reports',
  });
}

// 本月结算公里数计算
export async function currentMonthCalculationApi(params) {
  return request('api/car/mileage/agg/current/create', {
    method: 'POST',
    body: params,
    reqType: 'json',
    module: 'reports',
  });
}

/**
 * 订单明细导出
 */
export async function exportOrderDetail(params) {
  return request('api/car/mileage/detail/export', {
    method: 'POST',
    body: params,
    reqType: 'json',
    module: 'reports',
  });
}

/**
 * 订单明细分页查询
 */
export async function selectOrderDetail(params) {
  return request('api/car/mileage/detail/selectPage', {
    method: 'POST',
    body: params,
    reqType: 'json',
    module: 'reports',
  });
}
